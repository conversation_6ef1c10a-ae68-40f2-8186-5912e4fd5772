#!/usr/bin/env python3
"""
测试白名单功能修复
"""
import sys
import os
import json
import asyncio

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

async def test_whitelist_fix():
    """测试白名单功能修复"""
    try:
        print("=== 测试白名单功能修复 ===")
        
        from app.database import SessionLocal
        from app.services.ad_sync_config import get_ad_sync_config
        from app.schemas.ad import ADSyncFromPersonnel
        
        db = SessionLocal()
        
        # 1. 获取当前配置
        print("1. 获取当前AD同步配置...")
        config = await get_ad_sync_config(db)
        print(f"配置ID: {config.id}")
        print(f"禁用离职人员功能: {config.disable_inactive_users}")
        print(f"白名单原始数据: {repr(config.disable_whitelist)}")
        
        # 2. 测试手动同步的白名单处理逻辑（API端点逻辑）
        print("\n2. 测试手动同步的白名单处理逻辑...")
        disable_whitelist = config.disable_whitelist
        print(f"原始白名单数据: {disable_whitelist}，类型: {type(disable_whitelist)}")
        
        if isinstance(disable_whitelist, str):
            try:
                disable_whitelist = json.loads(disable_whitelist)
                print(f"JSON解析后的白名单数据: {disable_whitelist}")
            except json.JSONDecodeError:
                print(f"白名单数据格式错误: {disable_whitelist}，使用空列表")
                disable_whitelist = []
        elif disable_whitelist is None:
            print("白名单数据为None，使用空列表")
            disable_whitelist = []
        elif not isinstance(disable_whitelist, list):
            print(f"白名单数据类型错误: {type(disable_whitelist)}，使用空列表")
            disable_whitelist = []
        
        print(f"手动同步 - 最终传递给同步服务的白名单数据: {disable_whitelist}")
        
        # 3. 测试自动同步的白名单处理逻辑（修复后的main.py逻辑）
        print("\n3. 测试自动同步的白名单处理逻辑...")
        auto_disable_whitelist = config.disable_whitelist
        print(f"自动同步 - 原始白名单数据: {auto_disable_whitelist}，类型: {type(auto_disable_whitelist)}")
        
        if isinstance(auto_disable_whitelist, str):
            try:
                auto_disable_whitelist = json.loads(auto_disable_whitelist)
                print(f"自动同步 - JSON解析后的白名单数据: {auto_disable_whitelist}")
            except json.JSONDecodeError:
                print(f"自动同步 - 白名单数据格式错误: {auto_disable_whitelist}，使用空列表")
                auto_disable_whitelist = []
        elif auto_disable_whitelist is None:
            print("自动同步 - 白名单数据为None，使用空列表")
            auto_disable_whitelist = []
        elif not isinstance(auto_disable_whitelist, list):
            print(f"自动同步 - 白名单数据类型错误: {type(auto_disable_whitelist)}，使用空列表")
            auto_disable_whitelist = []
        
        print(f"自动同步 - 最终传递给同步服务的白名单数据: {auto_disable_whitelist}")
        
        # 4. 验证两种同步方式的白名单数据是否一致
        print("\n4. 验证两种同步方式的白名单数据一致性...")
        if disable_whitelist == auto_disable_whitelist:
            print("✅ 手动同步和自动同步的白名单数据一致")
        else:
            print("❌ 手动同步和自动同步的白名单数据不一致")
            print(f"  手动同步: {disable_whitelist}")
            print(f"  自动同步: {auto_disable_whitelist}")
        
        # 5. 创建同步数据对象测试
        print("\n5. 创建同步数据对象测试...")
        try:
            sync_data = ADSyncFromPersonnel(
                company_id=config.source_company_id,
                dept_id=config.source_dept_id,
                create_ou=config.create_ou,
                create_security_groups=config.create_security_groups,
                add_users_to_dept_groups=config.add_users_to_dept_groups,
                parent_ou_dn=config.target_ou_dn or "OU=Users,DC=example,DC=com",
                change_password_next_logon=config.change_password_next_logon,
                disable_inactive_users=config.disable_inactive_users,
                disable_whitelist=disable_whitelist,  # 使用处理后的白名单
                move_users_with_dept=config.move_users_with_dept,
                update_user_groups_with_dept=config.update_user_groups_with_dept,
                auto_rename_security_groups=config.auto_rename_security_groups
            )
            print("✅ 同步数据对象创建成功")
            print(f"同步数据中的白名单: {sync_data.disable_whitelist}")
            print(f"白名单类型: {type(sync_data.disable_whitelist)}")
        except Exception as e:
            print(f"❌ 同步数据对象创建失败: {e}")
        
        # 6. 模拟白名单逻辑处理
        print("\n6. 模拟白名单逻辑处理...")
        test_users = [
            {"JobNumber": "EMP001", "UserName": "张三", "Status": "离职"},
            {"JobNumber": "EMP002", "UserName": "李四", "Status": "离职"},
            {"JobNumber": "EMP003", "UserName": "王五", "Status": "离职"}
        ]
        
        # 模拟同步服务中的白名单处理逻辑
        whitelist = []
        if sync_data.disable_whitelist:
            try:
                if isinstance(sync_data.disable_whitelist, str):
                    whitelist = json.loads(sync_data.disable_whitelist)
                else:
                    whitelist = sync_data.disable_whitelist
                
                if whitelist:
                    print(f"禁用白名单已配置，包含 {len(whitelist)} 个工号: {', '.join(whitelist)}")
                else:
                    print("禁用白名单为空列表")
            except Exception as e:
                print(f"解析禁用白名单时出错: {str(e)}")
                whitelist = []
        else:
            print("未配置禁用白名单")
        
        # 处理每个用户
        disabled_count = 0
        whitelist_skipped_count = 0
        
        for user in test_users:
            if not user["JobNumber"]:
                continue
                
            print(f"\n检查用户: {user['JobNumber']} ({user['UserName']})")
            print(f"  白名单: {whitelist}")
            
            # 检查是否在白名单中
            if whitelist and user["JobNumber"] in whitelist:
                print(f"  ✅ 跳过禁用白名单中的用户: {user['JobNumber']} ({user['UserName']})")
                whitelist_skipped_count += 1
            else:
                print(f"  ❌ 将要禁用用户: {user['JobNumber']} ({user['UserName']})")
                disabled_count += 1
        
        print(f"\n模拟结果:")
        print(f"  - 将禁用: {disabled_count} 个用户")
        print(f"  - 白名单跳过: {whitelist_skipped_count} 个用户")
        
        db.close()
        print("\n✅ 白名单功能修复测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_whitelist_fix())
