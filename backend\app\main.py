from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from .api.v1 import auth, ad, ad_config, ad_sync_config, assets, asset_settings, inventory, field_values, ecology, ecology_sync, permissions, roles, terminal, email, email_personnel_sync, custom_fields, lock_management
from .api.v1 import api_router
from .database import engine, SessionLocal
from . import models
import logging
from sqlalchemy.orm import Session
from app.crud import ecology_user as ecology_crud
from datetime import datetime
import asyncio
import time
from app.services import ad_sync_config as ad_sync_config_service
from app.services import ad as ad_service
from app.schemas.ad import ADSyncFromPersonnel
import threading
from app.grpc.generate_pb import generate_proto_code
import os
from pathlib import Path
from app.middleware.redis_cache_logging import RedisCacheMiddleware
from app.services.lock_cleanup_service import lock_cleanup_service

# 配置日志 - 优化启动性能
logging.basicConfig(
    level=logging.WARNING,  # 启动时减少日志输出
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    encoding='utf-8',
    force=True
)

# 启动完成后恢复INFO级别
def restore_logging_level():
    logging.getLogger().setLevel(logging.INFO)
    logging.info("应用启动完成，已恢复INFO级别日志")

# 创建数据库表 - 已通过PostgreSQL迁移完成，注释掉自动创建
# models.Base.metadata.create_all(bind=engine)

# 初始化同步泛微用户数据（带锁机制）- 延迟执行优化
def init_ecology_sync():
    """延迟执行泛微用户数据初始化，避免影响启动速度"""
    def _delayed_init():
        db: Session = SessionLocal()
        try:
            # 检查初始化锁
            lock_name = "ecology_sync_lock"
            lock = db.query(models.InitializationLock).filter_by(lock_name=lock_name).first()

            # 如果锁不存在，创建一个新锁
            if not lock:
                lock = models.InitializationLock(lock_name=lock_name, is_initialized=False)
                db.add(lock)
                db.commit()
                db.refresh(lock)

            # 如果已经初始化过，直接返回
            if lock.is_initialized:
                logging.info(f"泛微用户数据已经初始化过（{lock.initialized_at}），跳过初始化")
                return

            # 检查是否有泛微用户数据
            users_count = db.query(models.EcologyUser).count()
            if users_count == 0:
                logging.info("未检测到泛微用户数据，开始初始化同步...")
                result = ecology_crud.sync_ecology_users(db)
                logging.info(f"初始化同步结果: {result}")
            else:
                logging.info(f"检测到已有 {users_count} 条泛微用户数据，无需初始化同步")

            # 更新锁状态
            lock.is_initialized = True
            lock.initialized_at = datetime.now()
            db.commit()
            logging.info(f"已更新初始化锁状态，标记为已初始化")

        except Exception as e:
            logging.error(f"初始化同步泛微用户数据失败: {str(e)}")
            db.rollback()
        finally:
            db.close()
    
    # 延迟5秒执行，避免影响启动速度
    threading.Timer(5.0, _delayed_init).start()

# 定时检查并执行AD自动同步
async def ad_sync_scheduler():
    logging.info("启动AD自动同步调度器")
    while True:
        try:
            # 创建新的数据库会话
            db = SessionLocal()

            # 检查是否需要执行同步
            need_sync = await ad_sync_config_service.is_sync_due(db)

            if need_sync:
                # 引入锁服务
                from app.services import ad_sync_lock as ad_sync_lock_service

                # 锁名称
                lock_name = "ad_personnel_sync"

                # 检查是否已有同步锁
                if await ad_sync_lock_service.is_locked(db, lock_name):
                    lock_info = await ad_sync_lock_service.get_lock_info(db, lock_name)
                    logging.warning(f"已有正在进行的同步操作，由 {lock_info.get('locked_by', '未知')} 于 {lock_info.get('locked_at', '未知时间')} 开始")
                    await asyncio.sleep(60)  # 等待1分钟后重试
                    db.close()
                    continue

                logging.info("开始执行AD自动同步...")

                # 获取同步配置
                config = await ad_sync_config_service.get_ad_sync_config(db)

                # 尝试获取锁
                lock_success = await ad_sync_lock_service.acquire_lock(db, lock_name, "system")
                if not lock_success:
                    logging.error("无法获取同步锁，跳过本次同步")
                    db.close()
                    continue

                try:
                    # 验证配置的有效性
                    from app.services.ad import validate_ou_dn
                    if not config.target_ou_dn or not await validate_ou_dn(config.target_ou_dn):
                        logging.error(f"目标OU '{config.target_ou_dn}' 无效或不存在，跳过本次同步")
                        await ad_sync_lock_service.release_lock(db, lock_name, "system")
                        db.close()
                        continue

                    # 处理依赖选项的逻辑关系
                    # 如果未启用创建安全组，则相关选项强制设为False
                    create_security_groups = config.create_security_groups
                    add_users_to_dept_groups = config.add_users_to_dept_groups if config.add_users_to_dept_groups is not None else False
                    update_user_groups_with_dept = config.update_user_groups_with_dept if config.update_user_groups_with_dept is not None else False
                    auto_rename_security_groups = config.auto_rename_security_groups if config.auto_rename_security_groups is not None else False

                    if not create_security_groups:
                        add_users_to_dept_groups = False
                        update_user_groups_with_dept = False
                        auto_rename_security_groups = False
                        logging.info("由于未启用创建安全组，已自动禁用相关依赖选项")

                    # 处理白名单数据类型
                    disable_whitelist = config.disable_whitelist
                    logging.info(f"自动同步 - 原始白名单数据: {disable_whitelist}，类型: {type(disable_whitelist)}")

                    if isinstance(disable_whitelist, str):
                        try:
                            import json
                            disable_whitelist = json.loads(disable_whitelist)
                            logging.info(f"自动同步 - JSON解析后的白名单数据: {disable_whitelist}")
                        except json.JSONDecodeError:
                            logging.warning(f"自动同步 - 白名单数据格式错误: {disable_whitelist}，使用空列表")
                            disable_whitelist = []
                    elif disable_whitelist is None:
                        logging.info("自动同步 - 白名单数据为None，使用空列表")
                        disable_whitelist = []
                    elif not isinstance(disable_whitelist, list):
                        logging.warning(f"自动同步 - 白名单数据类型错误: {type(disable_whitelist)}，使用空列表")
                        disable_whitelist = []

                    logging.info(f"自动同步 - 最终传递给同步服务的白名单数据: {disable_whitelist}")

                    # 创建同步数据，确保包含所有必要参数
                    sync_data = ADSyncFromPersonnel(
                        company_id=config.source_company_id,
                        dept_id=config.source_dept_id,
                        create_ou=config.create_ou,
                        create_security_groups=create_security_groups,
                        add_users_to_dept_groups=add_users_to_dept_groups,
                        parent_ou_dn=config.target_ou_dn,
                        change_password_next_logon=config.change_password_next_logon,
                        disable_inactive_users=config.disable_inactive_users,
                        disable_whitelist=disable_whitelist,
                        move_users_with_dept=config.move_users_with_dept,
                        update_user_groups_with_dept=update_user_groups_with_dept,
                        auto_rename_security_groups=auto_rename_security_groups
                    )

                    # 执行同步
                    result = await ad_service.sync_from_personnel(sync_data)

                    # 更新最后同步时间
                    await ad_sync_config_service.update_last_sync_time(db)

                    # 记录同步结果到日志
                    log_data = {
                        "operator": "系统自动同步",
                        "source_type": "company" if config.source_company_id else ("department" if config.source_dept_id else "all"),
                        "source_id": config.source_company_id or config.source_dept_id,
                        "result": result
                    }

                    # 确保result中包含完整的用户信息和密码
                    if "stats" in result and "new_users" in result["stats"] and len(result["stats"]["new_users"]) > 0:
                        logging.info(f"自动同步创建了 {len(result['stats']['new_users'])} 个新用户，将保存密码信息到日志")

                    # 添加同步日志
                    sync_log = await ad_sync_config_service.add_sync_log(db, log_data)

                    # 如果有新创建的用户，记录更详细的日志
                    if result.get("stats", {}).get("created_users", 0) > 0:
                        new_users_count = len(result.get("stats", {}).get("new_users", []))
                        logging.info(f"AD自动同步完成，创建了 {new_users_count} 个新用户，同步日志ID: {sync_log.id}")
                    else:
                        logging.info(f"AD自动同步完成，没有创建新用户，同步日志ID: {sync_log.id}")
                finally:
                    # 无论同步成功与否，都释放锁
                    await ad_sync_lock_service.release_lock(db, lock_name, "system")

            # 关闭数据库会话
            db.close()

            # 每1分钟检查一次
            await asyncio.sleep(60)

        except Exception as e:
            logging.error(f"AD自动同步调度器出错: {str(e)}")
            await asyncio.sleep(60)  # 发生错误后也等待1分钟


# 定时检查并执行人员邮箱自动同步
async def personnel_email_sync_scheduler():
    logging.info("启动人员邮箱自动同步调度器")
    while True:
        try:
            # 创建新的数据库会话
            db = SessionLocal()

            # 检查是否需要执行同步
            from app.services.personnel_sync_config import PersonnelSyncConfigService
            config_service = PersonnelSyncConfigService(db)
            need_sync = await config_service.is_sync_due()

            if need_sync:
                # 引入锁服务（复用AD同步的锁机制）
                from app.services import ad_sync_lock as sync_lock_service

                # 锁名称
                lock_name = "personnel_email_sync"

                # 检查是否已有同步锁
                if await sync_lock_service.is_locked(db, lock_name):
                    lock_info = await sync_lock_service.get_lock_info(db, lock_name)
                    logging.warning(f"已有正在进行的人员邮箱同步操作，由 {lock_info.get('locked_by', '未知')} 于 {lock_info.get('locked_at', '未知时间')} 开始")
                    await asyncio.sleep(60)  # 等待1分钟后重试
                    db.close()
                    continue

                logging.info("开始执行人员邮箱自动同步...")

                # 尝试获取锁
                lock_success = await sync_lock_service.acquire_lock(db, lock_name, "system")
                if not lock_success:
                    logging.error("无法获取人员邮箱同步锁，跳过本次同步")
                    db.close()
                    continue

                try:
                    # 执行同步
                    from app.services.personnel_email_sync import PersonnelEmailSyncService
                    sync_service = PersonnelEmailSyncService(db)

                    # 执行增量同步
                    result = await sync_service.sync_personnel_to_email(
                        full_sync=False,
                        dry_run=False
                    )

                    # 更新同步状态
                    if result.success:
                        await config_service.update_sync_status("success", "自动同步完成")
                        logging.info(f"人员邮箱自动同步完成，同步日志ID: {result.sync_log_id}")
                    else:
                        await config_service.update_sync_status("failed", "自动同步失败", result.error_message)
                        logging.error(f"人员邮箱自动同步失败: {result.error_message}")

                except Exception as sync_error:
                    logging.error(f"人员邮箱自动同步执行失败: {str(sync_error)}")
                    await config_service.update_sync_status("failed", "自动同步异常", str(sync_error))
                finally:
                    # 无论同步成功与否，都释放锁
                    await sync_lock_service.release_lock(db, lock_name, "system")

            # 关闭数据库会话
            db.close()

            # 每1分钟检查一次
            await asyncio.sleep(60)

        except Exception as e:
            logging.error(f"人员邮箱自动同步调度器出错: {str(e)}")
            await asyncio.sleep(60)  # 发生错误后也等待1分钟

# 启动gRPC服务器
def start_grpc_server():
    try:
        from app.grpc.server import serve
        logging.info("正在启动终端管理gRPC服务器...")
        serve()
    except Exception as e:
        logging.error(f"启动gRPC服务器失败: {str(e)}")

app = FastAPI(title="运维管理平台")

# 配置CORS - 开发环境允许所有源访问
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 开发环境允许所有源
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["Content-Disposition"]  # 允许前端访问 Content-Disposition 头
)

# 添加Redis缓存中间件 - 在CORS中间件之后添加
app.add_middleware(RedisCacheMiddleware, ttl=300)  # 默认缓存5分钟

# 启动后台定时任务 - 优化启动速度
@app.on_event("startup")
async def startup_event():
    logging.warning("应用启动中...")  # 使用WARNING级别确保显示
    
    # 1. 优先启动核心服务
    try:
        # 检查proto文件是否已编译，避免重复编译触发reload
        compiled_file = Path(__file__).parent / "grpc" / "terminal_pb" / "terminal_pb2.py"
        if not os.path.exists(compiled_file):
            logging.warning("开始编译终端管理gRPC协议文件...")
            generate_proto_code()
            logging.warning("gRPC协议文件编译完成")
    except Exception as e:
        logging.error(f"启动gRPC协议编译失败: {str(e)}")

    # 2. 启动gRPC服务器（后台线程）
    try:
        grpc_thread = threading.Thread(target=start_grpc_server, daemon=True)
        grpc_thread.start()
        logging.warning("gRPC服务器线程已启动")
    except Exception as e:
        logging.error(f"启动gRPC服务准备工作失败: {str(e)}")
    
    # 3. 延迟启动非关键服务
    async def delayed_services():
        await asyncio.sleep(3)  # 延迟3秒
        
        # 初始化同步泛微用户数据
        init_ecology_sync()
        
        # 启动SSE管理器后台任务
        from app.services.sse_manager import sse_manager
        await sse_manager.start_background_tasks()
        
        # 启动锁清理服务
        await lock_cleanup_service.start()
        
        # 启动AD同步调度器
        asyncio.create_task(ad_sync_scheduler())
        
        # 启动人员邮箱同步调度器
        asyncio.create_task(personnel_email_sync_scheduler())
        
        # 恢复日志级别
        restore_logging_level()
        
        logging.info("所有后台服务启动完成")
    
    # 后台启动延迟服务
    asyncio.create_task(delayed_services())
    
    logging.warning("应用核心服务启动完成，后台服务正在启动中...")

# 注册路由
app.include_router(auth.router, prefix="/api/v1/auth", tags=["认证"])
app.include_router(ad.router, prefix="/api/v1/ad/management", tags=["AD管理"])
app.include_router(ad_config.router, prefix="/api/v1/ad/config", tags=["AD配置"])
app.include_router(ad_sync_config.router, prefix="/api/v1/ad/sync-config", tags=["AD同步配置"])
app.include_router(assets.router, prefix="/api/v1/assets", tags=["资产"])
app.include_router(asset_settings.router, prefix="/api/v1/asset-settings", tags=["资产设置"])
app.include_router(inventory.router, prefix="/api/v1/inventory", tags=["资产盘点"])
app.include_router(field_values.router, prefix="/api/v1/field-values", tags=["字段值管理"])
app.include_router(ecology.router, prefix="/api/v1/ecology", tags=["泛微数据"])
app.include_router(ecology_sync.router, prefix="/api/v1/ecology-sync", tags=["泛微数据同步"])
app.include_router(permissions.router, prefix="/api/v1/permissions", tags=["权限"])
app.include_router(roles.router, prefix="/api/v1/roles", tags=["角色"])
app.include_router(email.router, prefix="/api/v1/email", tags=["企业邮箱"])
app.include_router(email_personnel_sync.router, prefix="/api/v1/email-personnel-sync", tags=["人员信息同步"])
# 添加新的人员邮箱同步路由
from app.api.v1 import personnel_email_sync, email_creation_requests, email_sync_management
app.include_router(personnel_email_sync.router, prefix="/api/v1/personnel-email-sync", tags=["人员邮箱同步"])
app.include_router(email_creation_requests.router, prefix="/api/v1/email-creation-requests", tags=["邮箱创建申请"])
app.include_router(email_sync_management.router, prefix="/api/v1/email-sync-management", tags=["邮箱同步锁管理"])
# 注册系统设置路由，不包含终端管理
app.include_router(api_router, prefix="/api/v1/system", tags=["系统设置"])

# 自定义字段管理 - 归属资产管理模块
app.include_router(custom_fields.router, prefix="/api/v1/custom-fields", tags=["自定义字段管理"])
# 单独注册终端管理路由，作为顶级路由
app.include_router(terminal.router, prefix="/api/v1/terminal", tags=["终端管理"])
# 注册表管理作为终端的子路由集成
from app.api.v1.registry import router as registry_router
app.include_router(registry_router, prefix="/api/v1/terminal", tags=["终端注册表管理"])
# 命令白名单管理作为顶级路由
from app.api.v1.command_whitelist import router as command_whitelist_router
app.include_router(command_whitelist_router, prefix="/api/v1/command-whitelist", tags=["命令白名单管理"])
# 锁管理API路由
app.include_router(lock_management.router, prefix="/api/v1", tags=["锁管理"])

# SSE路由（替代WebSocket）
from app.api.v1.sse import router as sse_router
app.include_router(sse_router, prefix="/api/v1/sse", tags=["Server-Sent Events"])

# 配置静态文件服务
# 确保uploads目录存在
os.makedirs("uploads", exist_ok=True)
os.makedirs("uploads/agent/windows", exist_ok=True)
os.makedirs("uploads/agent/linux", exist_ok=True)
os.makedirs("uploads/agent/macos", exist_ok=True)

# 将/downloads和/uploads路径都映射到uploads目录
app.mount("/downloads", StaticFiles(directory="uploads"), name="downloads")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时的清理工作"""
    logging.info("应用关闭，执行清理工作...")
    
    # 停止锁清理服务
    await lock_cleanup_service.stop()
    
    logging.info("清理工作完成")

@app.get("/")
async def root():
    return {"message": "运维管理平台API服务"}
