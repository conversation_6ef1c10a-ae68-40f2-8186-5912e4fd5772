from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta
import logging
from .. import models
from ..schemas.ad_config import ADSyncConfigCreate
from .. import schemas
import json
from sqlalchemy import func

logger = logging.getLogger(__name__)

async def calculate_next_sync_time(db: Session, config: models.ADSyncConfig = None) -> str:
    """计算下次同步时间"""
    try:
        if not config:
            config = await get_ad_sync_config(db)

        # 如果同步未启用，返回空
        if not config.enabled:
            return None

        # 获取当前时间
        now = datetime.now()

        # 如果设置了指定同步时间
        if config.sync_time:
            # 解析指定时间 (HH:MM格式)
            sync_hour, sync_minute = map(int, config.sync_time.split(':'))

            # 获取今天的同步时间点
            target_time_today = now.replace(hour=sync_hour, minute=sync_minute, second=0, microsecond=0)

            # 如果当前时间已经过了今天的同步时间点，计算明天的同步时间
            if now >= target_time_today:
                next_sync = target_time_today + timedelta(days=1)
            else:
                next_sync = target_time_today
        else:
            # 使用间隔小时方式
            # 如果从未同步过，以当前时间为起点
            if not config.last_sync_time:
                next_sync = now
            else:
                # 解析上次同步时间
                last_sync = datetime.strptime(config.last_sync_time, "%Y-%m-%d %H:%M:%S")

                # 计算下次同步时间
                next_sync = last_sync + timedelta(hours=config.sync_interval)

                # 如果下次同步时间已经过了，从当前时间开始计算
                if next_sync <= now:
                    next_sync = now + timedelta(hours=config.sync_interval)

        # 格式化为字符串
        return next_sync.strftime("%Y-%m-%d %H:%M:%S")
    except Exception as e:
        logger.error(f"计算下次同步时间失败: {str(e)}")
        return None

async def get_ad_sync_config(db: Session) -> models.ADSyncConfig:
    """获取AD同步配置"""
    config = db.query(models.ADSyncConfig).first()
    if not config:
        # 如果没有配置，返回一个空的配置对象
        return models.ADSyncConfig(
            id=0,
            enabled=False,
            source_company_id=None,
            source_dept_id=None,
            target_ou_dn="",
            create_ou=True,
            create_security_groups=False,
            add_users_to_dept_groups=False,
            change_password_next_logon=True,
            disable_inactive_users=True,
            disable_whitelist=None,
            move_users_with_dept=True,
            sync_interval=24,
            last_sync_time=None
        )

    # 处理白名单字段的JSON反序列化
    if config.disable_whitelist:
        try:
            import json
            if isinstance(config.disable_whitelist, str):
                # 如果是字符串，尝试解析JSON
                parsed_whitelist = json.loads(config.disable_whitelist)
                if isinstance(parsed_whitelist, list):
                    config.disable_whitelist = parsed_whitelist
                elif isinstance(parsed_whitelist, dict):
                    # 如果是字典格式，尝试从字典中提取列表
                    if 'value' in parsed_whitelist and isinstance(parsed_whitelist['value'], list):
                        config.disable_whitelist = parsed_whitelist['value']
                    elif 'items' in parsed_whitelist and isinstance(parsed_whitelist['items'], list):
                        config.disable_whitelist = parsed_whitelist['items']
                    else:
                        # 如果是字典但没有预期的键，尝试提取所有值
                        logger.warning(f"白名单数据为字典格式，尝试提取值: {parsed_whitelist}")
                        config.disable_whitelist = list(parsed_whitelist.values()) if parsed_whitelist else []
                else:
                    logger.warning(f"白名单数据格式错误，期望为列表或字典，实际为: {type(parsed_whitelist)}")
                    config.disable_whitelist = []
            elif isinstance(config.disable_whitelist, list):
                # 如果已经是列表，直接使用
                pass  # config.disable_whitelist 已经是正确格式
            elif isinstance(config.disable_whitelist, dict):
                # 如果是字典类型，尝试提取列表
                if 'value' in config.disable_whitelist and isinstance(config.disable_whitelist['value'], list):
                    config.disable_whitelist = config.disable_whitelist['value']
                elif 'items' in config.disable_whitelist and isinstance(config.disable_whitelist['items'], list):
                    config.disable_whitelist = config.disable_whitelist['items']
                else:
                    logger.warning(f"白名单数据为字典格式，尝试提取值: {config.disable_whitelist}")
                    config.disable_whitelist = list(config.disable_whitelist.values()) if config.disable_whitelist else []
            else:
                logger.warning(f"白名单数据类型异常: {type(config.disable_whitelist)}，使用空列表")
                config.disable_whitelist = []
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"解析白名单JSON数据失败: {e}")
            config.disable_whitelist = []
    else:
        config.disable_whitelist = []

    # 计算下次同步时间（不保存到数据库，只用于显示）
    config.next_sync_time = await calculate_next_sync_time(db, config)

    return config

async def update_ad_sync_config(db: Session, config: ADSyncConfigCreate) -> models.ADSyncConfig:
    """更新AD同步配置"""
    try:
        # 处理依赖选项的逻辑关系
        # 如果未启用创建安全组，则相关选项强制设为False
        if not config.create_security_groups:
            config.add_users_to_dept_groups = False
            config.update_user_groups_with_dept = False
            config.auto_rename_security_groups = False
            logger.info("由于未启用创建安全组，已自动禁用相关依赖选项")

        # 更新配置
        db_config = await get_ad_sync_config(db)
        if db_config.id == 0:  # 新配置
            # 处理白名单数据
            disable_whitelist_json = None
            if hasattr(config, 'disable_whitelist') and config.disable_whitelist is not None:
                try:
                    import json
                    if isinstance(config.disable_whitelist, list):
                        # 验证列表中的元素都是字符串
                        validated_whitelist = [str(item).strip() for item in config.disable_whitelist if str(item).strip()]
                        disable_whitelist_json = json.dumps(validated_whitelist)
                    elif isinstance(config.disable_whitelist, str):
                        disable_whitelist_json = config.disable_whitelist
                    else:
                        logger.warning(f"白名单数据类型错误，期望为list或str，实际为: {type(config.disable_whitelist)}")
                        disable_whitelist_json = json.dumps([])
                except Exception as e:
                    logger.error(f"处理白名单数据失败: {e}")
                    disable_whitelist_json = json.dumps([])
            
            db_config = models.ADSyncConfig(
                enabled=config.enabled,
                source_company_id=config.source_company_id,
                source_dept_id=config.source_dept_id,
                target_ou_dn=config.target_ou_dn,
                create_ou=config.create_ou,
                create_security_groups=config.create_security_groups,
                add_users_to_dept_groups=config.add_users_to_dept_groups,
                change_password_next_logon=config.change_password_next_logon,
                disable_inactive_users=config.disable_inactive_users,
                disable_whitelist=disable_whitelist_json,
                move_users_with_dept=config.move_users_with_dept,
                update_user_groups_with_dept=config.update_user_groups_with_dept,
                auto_rename_security_groups=config.auto_rename_security_groups,
                sync_interval=config.sync_interval,
                sync_time=config.sync_time,
                last_sync_time=None
            )
            db.add(db_config)
        else:  # 更新现有配置
            db_config.enabled = config.enabled
            db_config.source_company_id = config.source_company_id
            db_config.source_dept_id = config.source_dept_id
            db_config.target_ou_dn = config.target_ou_dn
            db_config.create_ou = config.create_ou
            db_config.create_security_groups = config.create_security_groups
            db_config.add_users_to_dept_groups = config.add_users_to_dept_groups
            db_config.change_password_next_logon = config.change_password_next_logon
            db_config.disable_inactive_users = config.disable_inactive_users
            # 处理白名单字段
            if hasattr(config, 'disable_whitelist') and config.disable_whitelist is not None:
                try:
                    import json
                    if isinstance(config.disable_whitelist, list):
                        # 验证列表中的元素都是字符串
                        validated_whitelist = [str(item).strip() for item in config.disable_whitelist if str(item).strip()]
                        db_config.disable_whitelist = json.dumps(validated_whitelist)
                    elif isinstance(config.disable_whitelist, str):
                        db_config.disable_whitelist = config.disable_whitelist
                    else:
                        logger.warning(f"白名单数据类型错误，期望为list或str，实际为: {type(config.disable_whitelist)}")
                        db_config.disable_whitelist = json.dumps([])
                except Exception as e:
                    logger.error(f"处理白名单数据失败: {e}")
                    db_config.disable_whitelist = json.dumps([])
            else:
                db_config.disable_whitelist = json.dumps([])
            db_config.move_users_with_dept = config.move_users_with_dept
            db_config.update_user_groups_with_dept = config.update_user_groups_with_dept
            db_config.auto_rename_security_groups = config.auto_rename_security_groups
            db_config.sync_interval = config.sync_interval
            db_config.sync_time = config.sync_time

        db.commit()
        db.refresh(db_config)
        return db_config
    except Exception as e:
        logger.error(f"更新AD同步配置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

async def update_last_sync_time(db: Session) -> models.ADSyncConfig:
    """更新最后同步时间"""
    try:
        db_config = await get_ad_sync_config(db)
        if db_config.id == 0:
            # 如果没有配置，创建一个新的
            return await update_ad_sync_config(db, ADSyncConfigCreate(
                enabled=False,
                target_ou_dn="",
                add_users_to_dept_groups=False
            ))

        # 更新最后同步时间
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        db_config.last_sync_time = current_time

        db.commit()
        db.refresh(db_config)
        return db_config
    except Exception as e:
        logger.error(f"更新最后同步时间失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

async def is_sync_due(db: Session) -> bool:
    """检查是否到了同步时间"""
    try:
        db_config = await get_ad_sync_config(db)

        # 如果同步未启用，直接返回False
        if not db_config.enabled:
            return False

        # 如果从未同步过，应该进行同步
        if not db_config.last_sync_time:
            return True

        # 获取当前时间
        now = datetime.now()

        # 如果设置了指定同步时间
        if db_config.sync_time:
            # 解析指定时间 (HH:MM格式)
            try:
                sync_hour, sync_minute = map(int, db_config.sync_time.split(':'))

                # 解析上次同步时间
                last_sync = datetime.strptime(db_config.last_sync_time, "%Y-%m-%d %H:%M:%S")

                # 获取目标同步时间（今天）
                target_time_today = now.replace(hour=sync_hour, minute=sync_minute, second=0, microsecond=0)

                # 如果当前时间已经超过了今天的同步时间，且上次同步时间早于今天的同步时间，则需要同步
                today_date = now.date()  # 获取今天的日期
                last_sync_date = last_sync.date()  # 获取上次同步的日期

                if today_date > last_sync_date:
                    # 如果上次同步是在前一天或更早
                    if now >= target_time_today:
                        # 如果当前时间已经过了今天的同步时间点，需要同步
                        return True
                    return False
                elif today_date == last_sync_date:
                    # 如果上次同步是今天
                    last_sync_time = last_sync.time()
                    target_time = target_time_today.time()

                    # 如果上次同步时间早于今天的目标同步时间，且当前时间已经过了目标同步时间，需要同步
                    return last_sync_time < target_time and now.time() >= target_time
                else:
                    # 理论上不会出现上次同步时间在未来的情况
                    return False
            except (ValueError, TypeError) as e:
                logger.error(f"解析同步时间失败: {str(e)}")
                return False
        else:
            # 使用传统的间隔小时方式
            # 解析上次同步时间
            last_sync = datetime.strptime(db_config.last_sync_time, "%Y-%m-%d %H:%M:%S")

            # 计算下次同步时间
            next_sync = last_sync + timedelta(hours=db_config.sync_interval)

            # 如果当前时间已经超过下次同步时间，则返回True
            return now >= next_sync
    except Exception as e:
        logger.error(f"检查同步时间失败: {str(e)}")
        return False

async def add_sync_log(db: Session, log_data: Dict[str, Any]) -> models.ADSyncLog:
    """添加同步日志"""
    try:
        # 获取统计信息，添加安全检查以防字段不存在
        stats = log_data.get("result", {}).get("stats", {})

        # 确保new_users数据存在于stats中
        if "new_users" not in stats and "new_users" in log_data.get("result", {}).get("stats", {}):
            stats["new_users"] = log_data["result"]["stats"]["new_users"]

        # 检查是否有实际变动
        has_changes = (
            stats.get("created_users", 0) > 0 or
            stats.get("disabled_users", 0) > 0 or
            stats.get("moved_users", 0) > 0 or
            stats.get("updated_users", 0) > 0 or
            stats.get("created_ous", 0) > 0 or
            stats.get("renamed_ous", 0) > 0 or
            stats.get("created_groups", 0) > 0 or
            stats.get("updated_groups", 0) > 0 or
            stats.get("added_to_groups", 0) > 0 or
            stats.get("removed_from_groups", 0) > 0 or
            len(stats.get("errors", [])) > 0
        )

        # 如果没有实际变动，且没有错误，则不记录详细日志
        if not has_changes and not stats.get("errors"):
            logger.info(f"同步操作没有实际变动，不记录详细日志")
            # 创建简化的日志记录
            db_log = models.ADSyncLog(
                operator=log_data["operator"],
                source_type=log_data["source_type"],
                source_id=log_data["source_id"],
                sync_time=datetime.now(),
                total_users=stats.get("total", 0),
                created_users=0,
                skipped_users=stats.get("skipped_users", 0),
                disabled_users=0,
                moved_users=0,
                updated_users=0,
                created_ous=0,
                renamed_ous=0,
                created_groups=0,
                updated_groups=0,
                added_to_groups=0,
                removed_from_groups=0,
                errors="[]",
                details=json.dumps({"message": "同步完成，没有实际变动", "stats": {"total": stats.get("total", 0)}}, ensure_ascii=False)
            )
        else:
            # 创建完整的日志记录
            db_log = models.ADSyncLog(
                operator=log_data["operator"],
                source_type=log_data["source_type"],
                source_id=log_data["source_id"],
                sync_time=datetime.now(),
                total_users=stats.get("total", 0),
                created_users=stats.get("created_users", 0),
                skipped_users=stats.get("skipped_users", 0),
                disabled_users=stats.get("disabled_users", 0),
                moved_users=stats.get("moved_users", 0),
                updated_users=stats.get("updated_users", 0),
                created_ous=stats.get("created_ous", 0),
                renamed_ous=stats.get("renamed_ous", 0),
                created_groups=stats.get("created_groups", 0),
                updated_groups=stats.get("updated_groups", 0),
                added_to_groups=stats.get("added_to_groups", 0),
                removed_from_groups=stats.get("removed_from_groups", 0),
                errors=json.dumps(stats.get("errors", []), ensure_ascii=False),
                details=json.dumps(log_data["result"], ensure_ascii=False)
            )

        db.add(db_log)
        db.commit()
        db.refresh(db_log)

        return db_log

    except Exception as e:
        logger.error(f"添加同步日志失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加同步日志失败: {str(e)}"
        )

async def get_sync_logs(db: Session, skip: int = 0, limit: int = 50) -> Dict[str, Any]:
    """获取同步日志"""
    try:
        # 查询总记录数
        total_count = db.query(func.count(models.ADSyncLog.id)).scalar()

        # 查询日志记录
        logs = db.query(models.ADSyncLog)\
                .order_by(models.ADSyncLog.sync_time.desc())\
                .offset(skip)\
                .limit(limit)\
                .all()

        # 转换为字典列表
        items = []
        for log in logs:
            # 解析JSON字符串为字典
            errors = json.loads(log.errors) if log.errors else []
            details = json.loads(log.details) if log.details else {}

            items.append({
                "id": log.id,
                "operator": log.operator,
                "source_type": log.source_type,
                "source_id": log.source_id,
                "sync_time": log.sync_time.strftime("%Y-%m-%d %H:%M:%S"),
                "total_users": log.total_users,
                "created_users": log.created_users,
                "skipped_users": log.skipped_users,
                "disabled_users": log.disabled_users,
                "moved_users": log.moved_users,
                "updated_users": log.updated_users,
                "created_ous": log.created_ous,
                "renamed_ous": log.renamed_ous,
                "created_groups": log.created_groups,
                "updated_groups": log.updated_groups,
                "added_to_groups": log.added_to_groups,
                "removed_from_groups": log.removed_from_groups,
                "errors": errors,
                "details": details
            })

        # 返回包含总数和列表的字典
        return {
            "items": items,
            "total": total_count
        }
    except Exception as e:
        logger.error(f"获取同步日志失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

async def get_sync_log_by_id(db: Session, log_id: int) -> Optional[models.ADSyncLog]:
    """根据ID获取同步日志"""
    try:
        # 查询特定ID的日志记录
        log = db.query(models.ADSyncLog).filter(models.ADSyncLog.id == log_id).first()

        if log and log.details:
            # 尝试解析JSON，确保它包含正确的结构
            try:
                details = json.loads(log.details)
                if "stats" in details and "new_users" not in details["stats"] and "new_users" in details:
                    # 兼容旧数据，确保new_users存在于stats中
                    details["stats"]["new_users"] = details["new_users"]
                    log.details = json.dumps(details, ensure_ascii=False)
            except:
                # 如果无法解析，不做处理
                pass

        return log
    except Exception as e:
        logger.error(f"获取同步日志失败: {str(e)}")
        return None