#!/usr/bin/env python3
"""
简单的白名单功能检查脚本
"""
import sys
import os
import json
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_database():
    """检查数据库中的配置"""
    try:
        from app.database import SessionLocal
        from app.models.ad_config import ADSyncConfig, ADSyncLog
        from app.models.ecology import EcologyUser
        from sqlalchemy import desc

        db = SessionLocal()
        print("✅ 数据库连接成功")

        print("=== 检查AD同步配置 ===")

        # 检查AD同步配置
        configs = db.query(ADSyncConfig).all()

        if not configs:
            print("⚠️ 未找到AD同步配置")
            return

        for config in configs:
            print(f"\n配置ID: {config.id}")
            print(f"启用状态: {config.enabled}")
            print(f"禁用离职人员: {config.disable_inactive_users}")
            print(f"白名单原始数据: {repr(config.disable_whitelist)}")

            # 尝试解析白名单
            if config.disable_whitelist:
                try:
                    if isinstance(config.disable_whitelist, str):
                        parsed = json.loads(config.disable_whitelist)
                        print(f"解析后白名单: {parsed}")
                        print(f"白名单包含 {len(parsed)} 个工号")
                    else:
                        print(f"白名单不是字符串: {type(config.disable_whitelist)}")
                except Exception as e:
                    print(f"白名单解析失败: {e}")
            else:
                print("白名单为空")

        print("\n=== 检查最近的同步日志 ===")

        # 检查同步日志
        logs = db.query(ADSyncLog).order_by(desc(ADSyncLog.sync_time)).limit(5).all()

        if not logs:
            print("⚠️ 未找到同步日志")
        else:
            for log in logs:
                print(f"\n日志ID: {log.id}")
                print(f"操作员: {log.operator}")
                print(f"同步时间: {log.sync_time}")
                print(f"禁用用户数: {log.disabled_users}")

                # 检查详情中的白名单信息
                if log.details:
                    try:
                        details_dict = json.loads(log.details)
                        stats = details_dict.get('stats', {})
                        whitelist_skipped = stats.get('whitelist_skipped_users', 0)
                        whitelist_details = stats.get('whitelist_skipped_users_details', [])

                        print(f"白名单跳过用户数: {whitelist_skipped}")
                        if whitelist_details:
                            print("白名单跳过用户详情:")
                            for user in whitelist_details[:3]:  # 只显示前3个
                                print(f"  - {user.get('username')} ({user.get('name')})")
                    except Exception as e:
                        print(f"详情解析失败: {e}")

        print("\n=== 检查离职人员数据 ===")

        # 检查离职人员
        inactive_users = db.query(EcologyUser).filter(
            EcologyUser.Status.in_(['离职', '停用', '禁用'])
        ).limit(10).all()

        if not inactive_users:
            print("⚠️ 未找到离职人员数据")
        else:
            print(f"找到 {len(inactive_users)} 个离职人员 (显示前10个):")
            for user in inactive_users:
                dept_name = getattr(user, 'DeptName', None) or 'N/A'
                print(f"  - {user.JobNumber} ({user.UserName}) - {user.Status} - {dept_name}")

        db.close()
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")

def simulate_whitelist_logic():
    """模拟白名单逻辑"""
    print("\n=== 模拟白名单逻辑 ===")
    
    # 模拟配置
    test_whitelist = ["EMP001", "EMP003"]
    
    # 模拟离职用户
    mock_users = [
        {"JobNumber": "EMP001", "UserName": "张三", "Status": "离职"},
        {"JobNumber": "EMP002", "UserName": "李四", "Status": "离职"},
        {"JobNumber": "EMP003", "UserName": "王五", "Status": "离职"},
        {"JobNumber": "EMP004", "UserName": "赵六", "Status": "离职"}
    ]
    
    print(f"测试白名单: {test_whitelist}")
    user_list = [f"{u['JobNumber']}({u['UserName']})" for u in mock_users]
    print(f"测试离职用户: {user_list}")
    
    # 模拟处理逻辑
    disabled_count = 0
    whitelist_skipped_count = 0
    
    for user in mock_users:
        if not user["JobNumber"]:
            continue
            
        print(f"\n检查用户: {user['JobNumber']} ({user['UserName']})")
        
        # 检查是否在白名单中
        if test_whitelist and user["JobNumber"] in test_whitelist:
            print(f"  ✅ 跳过禁用白名单中的用户")
            whitelist_skipped_count += 1
        else:
            print(f"  ❌ 将要禁用用户")
            disabled_count += 1
    
    print(f"\n模拟结果:")
    print(f"  - 将禁用: {disabled_count} 个用户")
    print(f"  - 白名单跳过: {whitelist_skipped_count} 个用户")

def main():
    """主函数"""
    print("=== AD同步白名单功能简单检查 ===")
    print(f"检查时间: {datetime.now()}")
    
    # 检查数据库
    check_database()
    
    # 模拟白名单逻辑
    simulate_whitelist_logic()
    
    print("\n=== 检查完成 ===")

if __name__ == "__main__":
    main()
