<template>
  <div class="ad-sync-config">
    <div class="page-header">
      <div class="title-area">
        <el-icon class="header-icon"><Connection /></el-icon>
        <h2 class="page-title">AD 同步配置</h2>
      </div>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>AD 管理</el-breadcrumb-item>
        <el-breadcrumb-item>同步配置</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <el-card class="box-card" shadow="hover">
      <el-tabs v-model="activeTab" class="tabs-container">
        <el-tab-pane label="同步设置" name="config">
          <el-form
            ref="formRef"
            :model="configForm"
            :rules="rules"
            label-width="180px"
          >
            <el-form-item prop="enabled">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'开启后，系统将根据设定的时间间隔或指定时间自动执行同步操作，保持AD用户与泛微系统人员信息的一致性。'"
                >
                  <span>启用自动同步</span>
                </el-tooltip>
              </template>
              <el-switch v-model="configForm.enabled" />
              <template #tip>
                <span class="el-form-item__tip">开启后将按设定的时间间隔自动同步人员信息</span>
              </template>
            </el-form-item>

            <el-form-item prop="syncSource">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'选择需要同步到AD的人员范围。可以同步所有人员，也可以按公司或部门进行筛选，仅同步指定范围内的人员。'"
                >
                  <span>同步来源</span>
                </el-tooltip>
              </template>
              <el-radio-group v-model="configForm.syncSource">
                <el-radio label="all">所有人员</el-radio>
                <el-radio label="company">按公司筛选</el-radio>
                <el-radio label="department">按部门筛选</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="configForm.syncSource === 'company'" prop="source_company_id">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'选择要同步的具体公司。系统将仅同步该公司中的所有员工信息到AD。'"
                >
                  <span>选择公司</span>
                </el-tooltip>
              </template>
              <el-select
                v-model="configForm.source_company_id"
                placeholder="请选择公司"
                filterable
              >
                <el-option
                  v-for="company in companies"
                  :key="company.id"
                  :label="company.name"
                  :value="company.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item v-if="configForm.syncSource === 'department'" prop="source_dept_id">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'选择要同步的具体部门。系统将仅同步该部门及其下级部门中的人员信息到AD。'"
                >
                  <span>选择部门</span>
                </el-tooltip>
              </template>
              <el-select
                v-model="configForm.source_dept_id"
                placeholder="请选择部门"
                filterable
              >
                <el-option
                  v-for="dept in departments"
                  :key="dept.id"
                  :label="dept.name"
                  :value="dept.id"
                />
              </el-select>
            </el-form-item>

            <el-form-item prop="target_ou_dn">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'选择要将用户同步到的AD组织单位(OU)。如果启用了创建组织结构，此OU将作为根节点，在其下创建部门层级结构；否则所有用户将直接放置在此OU下。'"
                >
                  <span>目标OU</span>
                </el-tooltip>
              </template>
              <div class="target-ou-container">
                <el-select
                  v-model="configForm.target_ou_dn"
                  placeholder="请选择目标OU"
                  filterable
                  style="width: 100%;"
                >
                  <el-option
                    v-for="ou in ouList"
                    :key="ou.dn"
                    :label="ou.displayName || ou.name"
                    :value="ou.dn"
                  />
                </el-select>
                <Authority permission="ad:sync">
                  <el-button type="primary" style="margin-left: 10px;" @click="fetchOUTree">刷新OU</el-button>
                </Authority>
              </div>
            </el-form-item>

            <el-form-item prop="create_ou">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'系统将只同步包含在职人员的部门，不会创建只有离职人员的旧部门。同步将保留完整的部门层级结构，确保子部门能够正确显示在其父部门下。'"
                >
                  <span>创建组织结构</span>
                </el-tooltip>
              </template>
              <el-switch v-model="configForm.create_ou" />
              <template #tip>
                <span class="el-form-item__tip">开启后，将根据泛微系统的完整组织结构层级创建对应的OU，仅同步包含在职人员的部门，不会创建只有离职人员的旧部门</span>
              </template>
            </el-form-item>

            <div class="info-box" v-if="!configForm.create_ou">
              <el-alert
                type="warning"
                show-icon
                :closable="false"
              >
                <template #title>
                  <strong>注意：</strong> 关闭此选项时，所有用户将直接放置在选择的目标OU下，而不会遵循其部门结构。
                </template>
                <div>如果希望用户按照其所属部门组织结构放置，请开启此选项。</div>
              </el-alert>
            </div>

            <el-form-item prop="create_security_groups">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'系统将为每个部门在AD中创建对应的安全组，并根据部门层级关系进行命名。这些安全组可用于实现基于部门的权限控制。'"
                >
                  <span>创建部门安全组</span>
                </el-tooltip>
              </template>
              <el-switch v-model="configForm.create_security_groups" />
              <template #tip>
                <span class="el-form-item__tip">启用后，将自动为每个部门创建对应的安全组（如果不存在）</span>
              </template>
            </el-form-item>

            <el-form-item prop="add_users_to_dept_groups">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'当用户被同步到AD时，会自动将其添加到所属部门的安全组中，便于通过安全组进行权限管理。用户调动部门时也会自动调整其所属安全组。'"
                >
                  <span>用户添加到部门安全组</span>
                </el-tooltip>
              </template>
              <el-switch v-model="configForm.add_users_to_dept_groups" :disabled="!configForm.create_security_groups" />
              <template #tip>
                <span class="el-form-item__tip">启用后，将自动将用户添加到其对应部门的安全组中（必须先启用"创建部门安全组"）</span>
              </template>
            </el-form-item>

            <el-form-item prop="change_password_next_logon">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'启用此选项后，新创建的AD用户首次登录Windows时将被强制要求修改密码，有助于提高账号安全性。用户必须设置新密码后才能正常使用系统。'"
                >
                  <span>首次登录修改密码</span>
                </el-tooltip>
              </template>
              <el-switch v-model="configForm.change_password_next_logon" />
              <template #tip>
                <span class="el-form-item__tip">启用后，新创建的用户在首次登录时将被要求修改密码</span>
              </template>
            </el-form-item>

            <el-form-item prop="disable_inactive_users">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'系统会自动检测人员在泛微系统中的状态，当状态变为离职时，会自动禁用其AD账号，保障信息安全。禁用的账号可以在AD管理控制台手动启用。'"
                >
                  <span>自动禁用离职人员</span>
                </el-tooltip>
              </template>
              <el-switch v-model="configForm.disable_inactive_users" />
              <template #tip>
                <span class="el-form-item__tip">启用后，当人员信息状态变为离职、解聘等非在职状态时，将自动禁用对应的AD账号</span>
              </template>
            </el-form-item>

            <!-- 禁用白名单配置 -->
            <el-form-item prop="disable_whitelist" v-show="configForm.disable_inactive_users">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'在此白名单中的离职人员工号将不会被自动禁用AD账号，适用于需要保留特定离职人员访问权限的情况。'"
                >
                  <span>禁用白名单</span>
                </el-tooltip>
              </template>
              <el-select
                v-model="configForm.disable_whitelist"
                multiple
                filterable
                allow-create
                placeholder="请输入或选择需要保留的离职人员工号"
                style="width: 100%"
              >
                <el-option
                  v-for="item in whitelist_options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              <template #tip>
                <span class="el-form-item__tip">输入工号并按回车添加到白名单，白名单中的离职人员不会被自动禁用AD账号</span>
              </template>
            </el-form-item>

            <el-form-item prop="move_users_with_dept">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'系统检测到用户在泛微系统中的部门发生变更时，会自动将用户在AD中移动到新部门对应的OU下，保持组织结构与人员关系的一致性。'"
                >
                  <span>部门变更自动移动用户</span>
                </el-tooltip>
              </template>
              <el-switch v-model="configForm.move_users_with_dept" />
              <template #tip>
                <span class="el-form-item__tip">启用后，当人员调动至其他部门时，将自动移动用户到新部门对应的OU</span>
              </template>
            </el-form-item>

            <el-form-item prop="update_user_groups_with_dept">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'当用户部门变更时，系统会自动将用户从原部门安全组中移除，并添加到新部门的安全组中，确保用户所属安全组与实际部门保持一致，便于管理部门级别的权限。'"
                >
                  <span>部门变更自动调整安全组</span>
                </el-tooltip>
              </template>
              <el-switch v-model="configForm.update_user_groups_with_dept" :disabled="!configForm.create_security_groups" />
              <template #tip>
                <span class="el-form-item__tip">启用后，当人员调动至其他部门时，将自动将用户从原部门安全组移动到新部门安全组（必须先启用"创建部门安全组"）</span>
              </template>
            </el-form-item>

            <el-form-item prop="auto_rename_security_groups">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'自动检测并修正那些被人为修改了名称但安全组描述中部门ID仍然存在的安全组。系统将根据标准规则恢复正确的安全组名称。'"
                >
                  <span>自动修正安全组名称</span>
                </el-tooltip>
              </template>
              <el-switch v-model="configForm.auto_rename_security_groups" :disabled="!configForm.create_security_groups" />
              <template #tip>
                <span class="el-form-item__tip">启用后，系统将检查并修正被人为修改的安全组名称，恢复为标准名称（必须先启用"创建部门安全组"）</span>
              </template>
            </el-form-item>

            <el-form-item prop="syncMode">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'设置自动同步的执行方式。按间隔时间：每隔指定小时数执行一次同步；指定时间：每天在固定时间点执行同步。'"
                >
                  <span>同步模式</span>
                </el-tooltip>
              </template>
              <el-radio-group v-model="configForm.syncMode">
                <el-radio label="interval">按间隔时间</el-radio>
                <el-radio label="specific">指定时间</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="configForm.syncMode === 'interval'" prop="sync_interval">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'设置每次自动同步的间隔时长，单位为小时。可设置范围为1-168小时（即1小时到7天）。'"
                >
                  <span>同步间隔(小时)</span>
                </el-tooltip>
              </template>
              <el-input-number
                v-model="configForm.sync_interval"
                :min="1"
                :max="168"
                :step="1"
              />
              <template #tip>
                <span class="el-form-item__tip">设置自动同步的时间间隔，单位为小时</span>
              </template>
            </el-form-item>

            <el-form-item v-if="configForm.syncMode === 'specific'" prop="sync_time">
              <template #label>
                <el-tooltip
                  effect="dark"
                  placement="right"
                  trigger="hover"
                  :show-after="1000"
                  :enterable="false"
                  :hide-after="0"
                  :content="'设置每天固定时间执行同步，例如每天16:30。建议选择非工作高峰期时段执行，以减少对系统性能的影响。'"
                >
                  <span>同步时间</span>
                </el-tooltip>
              </template>
              <el-time-picker
                v-model="syncTimeValue"
                format="HH:mm"
                placeholder="选择时间"
                @change="handleSyncTimeChange"
              />
              <template #tip>
                <span class="el-form-item__tip">设置每天指定时间同步，例如每天16:30</span>
              </template>
            </el-form-item>

            <el-form-item v-if="configForm.last_sync_time" label="上次同步时间">
              <span>{{ configForm.last_sync_time }}</span>
            </el-form-item>

            <el-form-item v-if="configForm.enabled && configForm.next_sync_time" label="下次同步时间">
              <span>{{ configForm.next_sync_time }}</span>
            </el-form-item>

            <el-form-item>
              <Authority permission="ad:sync">
                <el-button type="primary" @click="handleSubmit">保存配置</el-button>
              </Authority>
              <Authority permission="ad:sync">
                <el-button type="success" @click="handleManualSync">立即同步</el-button>
              </Authority>
            </el-form-item>
          </el-form>
        </el-tab-pane>

        <el-tab-pane label="同步日志" name="logs">
          <el-card class="log-card">
            <template #header>
              <div class="card-header">
                <div></div>
                <Authority permission="ad:sync">
                  <el-button type="primary" size="small" @click="() => { pagination.currentPage = 1; fetchSyncLogs() }">
                    <el-icon class="mr-1"><Refresh /></el-icon>刷新
                  </el-button>
                </Authority>
              </div>
            </template>

            <div v-if="syncLogs.length === 0" class="empty-state">
              <el-empty description="暂无同步日志" />
            </div>

            <el-table
              v-else
              :data="syncLogs"
              stripe
              style="width: 100%"
              border
              :cell-style="{ padding: '8px 0' }"
              :header-cell-style="{
                backgroundColor: '#f5f7fa',
                color: '#606266',
                fontWeight: 'bold',
                height: '50px',
                padding: '0 5px'
              }"
              :row-style="{ height: '48px' }"
            >
              <el-table-column prop="sync_time" label="同步时间" min-width="180" />
              <el-table-column prop="operator" label="操作员" min-width="120" />
              <el-table-column label="同步来源" min-width="180">
                <template #default="scope">
                  <span v-if="scope.row.source_type === 'all'">所有人员</span>
                  <span v-else-if="scope.row.source_type === 'company'">
                    公司：{{ getCompanyName(scope.row.source_id) }}
                  </span>
                  <span v-else-if="scope.row.source_type === 'department'">
                    部门：{{ getDepartmentName(scope.row.source_id) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="total_users" label="总人数" min-width="80" />
              <el-table-column prop="created_users" label="新增用户" min-width="80" />
              <el-table-column prop="updated_users" label="更新用户" min-width="80" />
              <el-table-column prop="moved_users" label="移动用户" min-width="80" />
              <el-table-column prop="skipped_users" label="跳过用户" min-width="80" />
              <el-table-column prop="disabled_users" label="禁用离职账号" min-width="80" />
              <el-table-column prop="created_ous" label="创建OU" min-width="80" />
              <el-table-column prop="renamed_ous" label="重命名OU" min-width="80" />
              <el-table-column prop="created_groups" label="创建安全组" min-width="95" />
              <el-table-column prop="updated_groups" label="更新安全组" min-width="95" />
              <el-table-column prop="added_to_groups" label="添加到安全组" min-width="110" />
              <el-table-column prop="removed_from_groups" label="移除安全组" min-width="95" />
              <el-table-column label="操作" min-width="160" fixed="right">
                <template #default="scope">
                  <el-button type="primary" size="small" text @click="showDetails(scope.row)">详情</el-button>
                  <Authority permission="ad:view">
                    <el-button
                      v-if="scope.row.created_users > 0"
                      size="small"
                      type="success"
                      text
                      @click="exportLogPasswords(scope.row)"
                    >
                      <el-icon><Download /></el-icon> 导出
                    </el-button>
                  </Authority>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container" v-if="syncLogs.length > 0">
              <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                layout="total, sizes, prev, pager, next, jumper"
                :total="pagination.total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 详情对话框 -->
    <el-dialog
      v-model="detailsVisible"
      title="同步详情"
      width="80%"
    >
      <template v-if="selectedLog">
        <!-- 选项卡结构 -->
        <el-tabs v-model="activeLogTab" class="detail-tabs">
          <!-- 基本信息选项卡 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="同步时间">{{ selectedLog.sync_time }}</el-descriptions-item>
              <el-descriptions-item label="操作员">{{ selectedLog.operator }}</el-descriptions-item>
              <el-descriptions-item label="同步来源">
                <span v-if="selectedLog.source_type === 'all'">所有人员</span>
                <span v-else-if="selectedLog.source_type === 'company'">
                  公司：{{ getCompanyName(selectedLog.source_id) }}
                </span>
                <span v-else-if="selectedLog.source_type === 'department'">
                  部门：{{ getDepartmentName(selectedLog.source_id) }}
                </span>
              </el-descriptions-item>
              <el-descriptions-item label="总人数">{{ selectedLog.total_users }}</el-descriptions-item>
              <el-descriptions-item label="新增用户">{{ selectedLog.created_users }}</el-descriptions-item>
              <el-descriptions-item label="更新用户">{{ selectedLog.updated_users || 0 }}</el-descriptions-item>
              <el-descriptions-item label="移动用户">{{ selectedLog.moved_users || 0 }}</el-descriptions-item>
              <el-descriptions-item label="跳过用户">{{ selectedLog.skipped_users }}</el-descriptions-item>
              <el-descriptions-item label="禁用离职账号">{{ selectedLog.disabled_users || 0 }}</el-descriptions-item>
              <el-descriptions-item label="创建OU">{{ selectedLog.created_ous || 0 }}</el-descriptions-item>
              <el-descriptions-item label="重命名OU">{{ selectedLog.renamed_ous || 0 }}</el-descriptions-item>
              <el-descriptions-item label="创建安全组">{{ selectedLog.created_groups || 0 }}</el-descriptions-item>
              <el-descriptions-item label="更新安全组">{{ selectedLog.updated_groups || 0 }}</el-descriptions-item>
              <el-descriptions-item label="添加到安全组">{{ selectedLog.added_to_groups || 0 }}</el-descriptions-item>
              <el-descriptions-item label="移除安全组">{{ selectedLog.removed_from_groups || 0 }}</el-descriptions-item>
            </el-descriptions>
          </el-tab-pane>

          <!-- 错误信息选项卡 -->
          <el-tab-pane label="错误信息" name="errors">
            <template v-if="selectedLog.errors && selectedLog.errors.length > 0">
              <el-card>
                <div v-for="(error, index) in selectedLog.errors" :key="index" class="error-item">
                  {{ error }}
                </div>
              </el-card>
            </template>
            <el-empty v-else description="无错误信息" />
          </el-tab-pane>

          <!-- 用户操作选项卡 -->
          <el-tab-pane label="用户操作" name="users">
            <!-- 新创建的用户信息 -->
            <template v-if="selectedLog.details && selectedLog.details.new_users && selectedLog.details.new_users.length > 0">
              <h4>新创建的用户</h4>
              <div class="actions-container">
                <Authority permission="ad:view">
                  <el-button size="small" type="primary" @click="handleExportPasswords('xlsx')">
                    <el-icon><Download /></el-icon> 导出Excel
                  </el-button>
                  <el-button size="small" @click="handleExportPasswords('csv')">
                    <el-icon><Download /></el-icon> 导出CSV
                  </el-button>
                </Authority>
              </div>
              <el-table :data="selectedLog.details.new_users" stripe style="width: 100%">
                <el-table-column prop="username" label="用户名" width="120" />
                <el-table-column prop="name" label="姓名" width="120" />
                <el-table-column prop="department" label="部门" width="180" />
                <el-table-column prop="password" label="初始密码" width="120" />
              </el-table>
            </template>

            <!-- 禁用的用户信息 -->
            <template v-if="selectedLog.details && ((selectedLog.details.disabled_users_details && selectedLog.details.disabled_users_details.length > 0) ||
                          (selectedLog.details.stats && selectedLog.details.stats.disabled_users_details && selectedLog.details.stats.disabled_users_details.length > 0))">
              <h4>禁用的离职账号</h4>
              <el-table :data="selectedLog.details.disabled_users_details || selectedLog.details.stats.disabled_users_details" stripe style="width: 100%">
                <el-table-column prop="username" label="用户名" width="120" />
                <el-table-column prop="name" label="姓名" width="120" />
                <el-table-column prop="department" label="部门" width="150" />
                <el-table-column prop="status" label="人员状态" width="100" />
              </el-table>
            </template>

            <!-- 兼容处理：如果没有详细信息但有禁用账号数 -->
            <template v-else-if="selectedLog && selectedLog.disabled_users && selectedLog.disabled_users > 0 &&
                              (!selectedLog.details ||
                              (!selectedLog.details.disabled_users_details &&
                               (!selectedLog.details.stats || !selectedLog.details.stats.disabled_users_details)))">
              <h4>禁用的离职账号</h4>
              <el-alert
                type="info"
                :closable="false"
                show-icon
              >
                <p>本次同步共禁用了 {{ selectedLog.disabled_users }} 个账号。</p>
                <p>该同步日志为旧版本生成，不包含禁用账号的详细信息。请进行新的同步操作查看详细信息。</p>
              </el-alert>
            </template>

            <!-- 更新的用户信息 -->
            <template v-if="selectedLog.details &&
                          ((selectedLog.details.updated_users_details && selectedLog.details.updated_users_details.length > 0) ||
                           (selectedLog.details.stats && selectedLog.details.stats.updated_users_details && selectedLog.details.stats.updated_users_details.length > 0) ||
                           (selectedLog.updated_users && Array.isArray(selectedLog.updated_users) && (selectedLog.updated_users as any[]).length > 0))">
              <h4>更新的用户</h4>
              <el-table
                :data="selectedLog.details.updated_users_details ||
                       (selectedLog.details.stats && selectedLog.details.stats.updated_users_details) ||
                       selectedLog.updated_users || []"
                stripe
                style="width: 100%">
                <el-table-column prop="username" label="用户名" width="120" />
                <el-table-column prop="name" label="姓名" width="120" />
                <el-table-column prop="department" label="部门" width="150" />
                <el-table-column label="更新详情" min-width="300">
                  <template #default="scope">
                    <div v-if="scope.row.changes && scope.row.changes.length > 0">
                      <div v-for="(change, index) in scope.row.changes" :key="index" class="field-change">
                        <strong>{{ change.field }}:</strong>
                        <span class="old-value">{{ change.old_value || '(空)' }}</span>
                        <span class="arrow">→</span>
                        <span class="new-value">{{ change.new_value || '(空)' }}</span>
                      </div>
                    </div>
                    <div v-else>{{ scope.row.changed_fields || '字段更新' }}</div>
                  </template>
                </el-table-column>
              </el-table>
            </template>

            <!-- 移动的用户信息 -->
            <template v-if="selectedLog.details && selectedLog.details.moved_users && selectedLog.details.moved_users.length > 0">
              <h4>部门调动的用户</h4>
              <el-table :data="selectedLog.details.moved_users" stripe style="width: 100%">
                <el-table-column prop="username" label="用户名" width="120" />
                <el-table-column prop="name" label="姓名" width="120" />
                <el-table-column prop="old_department" label="原部门" width="180" />
                <el-table-column prop="new_department" label="新部门" width="180" />
              </el-table>
            </template>

            <el-empty v-if="!hasUserOperations" description="无用户操作记录" />
          </el-tab-pane>

          <!-- 组织结构操作选项卡 -->
          <el-tab-pane label="组织结构操作" name="org">
            <!-- 创建的OU信息 -->
            <template v-if="selectedLog.details && selectedLog.details.created_ous && selectedLog.details.created_ous.length > 0">
              <h4>创建的组织单位(OU)</h4>
              <el-table :data="selectedLog.details.created_ous" stripe style="width: 100%">
                <el-table-column prop="name" label="OU名称" width="150" />
                <el-table-column prop="dn" label="DN" min-width="250" show-overflow-tooltip />
              </el-table>
            </template>

            <!-- 重命名的OU信息 -->
            <template v-if="selectedLog.details && selectedLog.details.renamed_ous && selectedLog.details.renamed_ous.length > 0">
              <h4>重命名的组织单位(OU)</h4>
              <el-table :data="selectedLog.details.renamed_ous" stripe style="width: 100%">
                <el-table-column prop="old_name" label="原名称" width="150" />
                <el-table-column prop="new_name" label="新名称" width="150" />
                <el-table-column prop="dn" label="DN" min-width="250" show-overflow-tooltip />
              </el-table>
            </template>

            <el-empty v-if="!hasOrgOperations" description="无组织结构操作记录" />
          </el-tab-pane>

          <!-- 安全组操作选项卡 -->
          <el-tab-pane label="安全组操作" name="groups">
            <!-- 创建的安全组信息 -->
            <template v-if="selectedLog.details && selectedLog.details.created_groups && selectedLog.details.created_groups.length > 0">
              <h4>创建的安全组</h4>
              <el-table :data="selectedLog.details.created_groups" stripe style="width: 100%">
                <el-table-column prop="name" label="组名称" width="180" />
                <el-table-column prop="dn" label="DN" min-width="250" show-overflow-tooltip />
              </el-table>
            </template>

            <!-- 更新的安全组信息 -->
            <template v-if="selectedLog.details && selectedLog.details.updated_groups && selectedLog.details.updated_groups.length > 0">
              <h4>更新的安全组</h4>
              <el-table :data="selectedLog.details.updated_groups" stripe style="width: 100%">
                <el-table-column prop="name" label="组名称" width="180" />
                <el-table-column prop="changed_fields" label="更新字段" width="150" />
              </el-table>
            </template>

            <!-- 用户添加到安全组的信息 -->
            <template v-if="selectedLog.details && selectedLog.details.added_to_groups && selectedLog.details.added_to_groups.length > 0">
              <h4>添加到安全组的用户</h4>
              <el-table :data="selectedLog.details.added_to_groups" stripe style="width: 100%">
                <el-table-column prop="username" label="用户名" width="120" />
                <el-table-column prop="name" label="用户姓名" width="120" />
                <el-table-column prop="group_name" label="安全组" width="180" />
              </el-table>
            </template>

            <!-- 从安全组移除的用户信息 -->
            <template v-if="selectedLog.details && selectedLog.details.removed_from_groups && selectedLog.details.removed_from_groups.length > 0">
              <h4>从安全组移除的用户</h4>
              <el-table :data="selectedLog.details.removed_from_groups" stripe style="width: 100%">
                <el-table-column prop="username" label="用户名" width="120" />
                <el-table-column prop="name" label="用户姓名" width="120" />
                <el-table-column prop="group_name" label="安全组" width="180" />
              </el-table>
            </template>

            <el-empty v-if="!hasGroupOperations" description="无安全组操作记录" />
          </el-tab-pane>
        </el-tabs>
      </template>

      <!-- 底部操作按钮区 -->
      <template #footer>
        <div class="dialog-footer">
          <Authority permission="ad:view">
            <el-button type="primary" @click="exportFullLog('xlsx')">
              <el-icon><Download /></el-icon> 导出完整日志(Excel)
            </el-button>
            <el-button @click="exportFullLog('csv')">
              <el-icon><Download /></el-icon> 导出完整日志(CSV)
            </el-button>
          </Authority>
          <el-button @click="detailsVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import {
  getADSyncConfig,
  updateADSyncConfig,
  runManualADSync,
  getOUTree,
  getADSyncLogs,
  type ADSyncLog,
  exportSyncLogPasswords,
  exportSyncLogFullDetails
} from '@/api/ad'
import { getCompanies, getDepartments } from '@/api/ecology'
import { Download, Refresh, Connection } from '@element-plus/icons-vue'
import Authority from '@/components/Authority/index.vue'

// 当前激活的标签
const activeTab = ref('config')
// 详情对话框中当前激活的选项卡
const activeLogTab = ref('basic')

const formRef = ref<FormInstance | null>(null)
const configForm = ref({
  id: 0,
  enabled: false,
  syncSource: 'all', // 用于UI选择
  syncMode: 'interval', // 同步模式：interval按间隔，specific按指定时间
  source_company_id: null as number | null,
  source_dept_id: null as number | null,
  target_ou_dn: '',
  create_ou: true,
  create_security_groups: false,
  add_users_to_dept_groups: false,
  change_password_next_logon: true,
  disable_inactive_users: true,
  disable_whitelist: [] as string[],
  move_users_with_dept: true,
  update_user_groups_with_dept: false, // 修改默认值为false，与后端保持一致
  auto_rename_security_groups: false,
  sync_interval: 24,
  sync_time: null as string | null,
  last_sync_time: null as string | null,
  next_sync_time: null as string | null
})

// 同步时间值
const syncTimeValue = ref<Date | null>(null)

// 组织机构列表
const ouList = ref<{ dn: string; name: string; displayName?: string }[]>([])
// 公司列表
const companies = ref<{ id: number; name: string }[]>([])
// 部门列表
const departments = ref<{ id: number; name: string }[]>([])
// 同步日志
const syncLogs = ref<ADSyncLog[]>([])
// 分页信息
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})
// 详情对话框
const detailsVisible = ref(false)
// 当前选中的日志
const selectedLog = ref<ADSyncLog | null>(null)
// 白名单选项
const whitelist_options = ref<{ label: string; value: string }[]>([])

// 表单验证规则
const rules = {
  target_ou_dn: [
    { required: true, message: '请选择目标OU', trigger: 'change' }
  ],
  sync_interval: [
    { required: true, type: 'number', message: '请输入同步间隔', trigger: 'blur' },
    { type: 'number', min: 1, max: 168, message: '同步间隔必须在1-168小时之间', trigger: 'blur' }
  ]
}

// 计算属性：判断是否存在用户操作
const hasUserOperations = computed(() => {
  if (!selectedLog.value) return false;

  // 检查是否有详情数据
  if (selectedLog.value.details) {
    const details = selectedLog.value.details;
    return !!(
      (details.new_users && details.new_users.length > 0) ||
      (details.disabled_users_details && details.disabled_users_details.length > 0) ||
      (details.stats && details.stats.disabled_users_details && details.stats.disabled_users_details.length > 0) ||
      (details.updated_users && details.updated_users.length > 0) ||
      (details.updated_users_details && details.updated_users_details.length > 0) ||
      (details.stats && details.stats.updated_users_details && details.stats.updated_users_details.length > 0) ||
      (details.moved_users && details.moved_users.length > 0)
    );
  }

  // 检查顶层数据
  return !!(
    selectedLog.value.updated_users && Array.isArray(selectedLog.value.updated_users) && selectedLog.value.updated_users.length > 0
  );
});

// 计算属性：判断是否存在组织结构操作
const hasOrgOperations = computed(() => {
  if (!selectedLog.value || !selectedLog.value.details) return false;
  const details = selectedLog.value.details;
  return !!(
    (details.created_ous && details.created_ous.length > 0) ||
    (details.renamed_ous && details.renamed_ous.length > 0)
  );
});

// 计算属性：判断是否存在安全组操作
const hasGroupOperations = computed(() => {
  if (!selectedLog.value || !selectedLog.value.details) return false;
  const details = selectedLog.value.details;
  return !!(
    (details.created_groups && details.created_groups.length > 0) ||
    (details.updated_groups && details.updated_groups.length > 0) ||
    (details.added_to_groups && details.added_to_groups.length > 0) ||
    (details.removed_from_groups && details.removed_from_groups.length > 0)
  );
});

// 获取OU树
const fetchOUTree = async () => {
  try {
    ElMessage.info('正在获取OU树数据...')
    const response = await getOUTree()
    if (response && response.data) {
      console.log('获取到OU树数据:', response.data)
      processOUTree(response.data)
      ElMessage.success(`获取到${ouList.value.length}个OU`)
    } else {
      console.warn('获取OU树失败：', response)
      ElMessage.warning('获取OU树失败，请先确认AD配置')
    }
  } catch (error) {
    console.error('获取OU树失败:', error)
    ElMessage.error(`获取OU树失败: ${error instanceof Error ? error.message : String(error)}`)
  }
}

// 处理OU树数据，将其转换为扁平结构用于下拉选择
const processOUTree = (tree: any) => {
  const result: { dn: string; name: string; displayName?: string }[] = []

  // 如果tree为空，提前返回
  if (!tree || !Array.isArray(tree)) {
    console.warn('OU树数据为空或格式不正确')
    ouList.value = []
    return
  }

  const traverse = (nodes: any[], level = 0) => {
    if (!nodes || !Array.isArray(nodes)) return

    nodes.forEach(node => {
      if (node) {
        // 添加当前节点
        const prefix = level > 0 ? '├─ '.repeat(level) : '';

        result.push({
          dn: node.dn,
          name: node.name,
          displayName: `${prefix}${node.name}`
        })

        // 遍历子节点
        if (node.children && node.children.length > 0) {
          traverse(node.children, level + 1)
        }
      }
    })
  }

  traverse(tree)
  ouList.value = result
}

// 获取公司列表
const fetchCompanies = async () => {
  try {
    const response = await getCompanies()
    companies.value = response.data
  } catch (error) {
    console.error('获取公司列表失败:', error)
    ElMessage.error('获取公司列表失败')
  }
}

// 获取部门列表
const fetchDepartments = async () => {
  try {
    const response = await getDepartments()
    departments.value = response.data
  } catch (error) {
    console.error('获取部门列表失败:', error)
    ElMessage.error('获取部门列表失败')
  }
}

// 获取同步配置
const fetchConfig = async () => {
  try {
    const response = await getADSyncConfig()
    const data = response.data

    // 设置同步来源选项
    let syncSource = 'all'
    if (data.source_company_id) {
      syncSource = 'company'
    } else if (data.source_dept_id) {
      syncSource = 'department'
    }

    // 设置同步模式
    let syncMode = 'interval'
    if (data.sync_time) {
      syncMode = 'specific'
      // 如果有具体时间，转换为Date对象用于时间选择器
      if (data.sync_time) {
        const [hours, minutes] = data.sync_time.split(':').map(Number)
        const date = new Date()
        date.setHours(hours, minutes, 0, 0)
        syncTimeValue.value = date
      }
    }

    configForm.value = {
      id: data.id || 0,
      enabled: data.enabled,
      syncSource: syncSource,
      syncMode: syncMode,
      source_company_id: data.source_company_id || null,
      source_dept_id: data.source_dept_id || null,
      target_ou_dn: data.target_ou_dn || '',
      create_ou: data.create_ou,
      create_security_groups: data.create_security_groups || false,
      add_users_to_dept_groups: data.add_users_to_dept_groups || false,
      change_password_next_logon: data.change_password_next_logon,
      disable_inactive_users: data.disable_inactive_users,
      disable_whitelist: data.disable_whitelist || [],
      move_users_with_dept: data.move_users_with_dept,
      update_user_groups_with_dept: data.update_user_groups_with_dept !== undefined ? data.update_user_groups_with_dept : false,
      auto_rename_security_groups: data.auto_rename_security_groups || false,
      sync_interval: data.sync_interval,
      sync_time: data.sync_time || null,
      last_sync_time: data.last_sync_time || null,
      next_sync_time: data.next_sync_time || null
    }
  } catch (error) {
    console.error('获取AD同步配置失败:', error)
    ElMessage.error('获取配置失败')
  }
}

// 保存配置
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    // 表单验证
    await formRef.value.validate()

    // 根据syncSource设置相应的来源ID
    if (configForm.value.syncSource === 'all') {
      configForm.value.source_company_id = null
      configForm.value.source_dept_id = null
    } else if (configForm.value.syncSource === 'company') {
      configForm.value.source_dept_id = null
    } else if (configForm.value.syncSource === 'department') {
      configForm.value.source_company_id = null
    }

    // 根据同步模式设置相应的字段
    if (configForm.value.syncMode === 'interval') {
      configForm.value.sync_time = null
    } else if (configForm.value.syncMode === 'specific') {
      // 使用指定时间同步时，同步间隔设为24小时（每天）
      configForm.value.sync_interval = 24
    }

    // 发送更新请求
    await updateADSyncConfig({
      id: configForm.value.id,
      enabled: configForm.value.enabled,
      source_company_id: configForm.value.source_company_id,
      source_dept_id: configForm.value.source_dept_id,
      target_ou_dn: configForm.value.target_ou_dn,
      create_ou: configForm.value.create_ou,
      create_security_groups: configForm.value.create_security_groups,
      add_users_to_dept_groups: configForm.value.add_users_to_dept_groups,
      change_password_next_logon: configForm.value.change_password_next_logon,
      disable_inactive_users: configForm.value.disable_inactive_users,
      disable_whitelist: configForm.value.disable_whitelist,
      move_users_with_dept: configForm.value.move_users_with_dept,
      update_user_groups_with_dept: configForm.value.update_user_groups_with_dept,
      auto_rename_security_groups: configForm.value.auto_rename_security_groups,
      sync_interval: configForm.value.sync_interval,
      sync_time: configForm.value.sync_time
    })

    ElMessage.success('保存成功')

    // 重新获取配置
    fetchConfig()
  } catch (error) {
    console.error('保存AD同步配置失败:', error)
    ElMessage.error('保存失败')
  }
}

// 手动执行同步
const handleManualSync = async () => {
  try {
    // 确认对话框
    await ElMessageBox.confirm(
      '确定要立即执行同步操作吗？',
      '确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 执行同步
    const response = await runManualADSync()

    ElMessage.success('同步成功')

    // 显示同步结果
    console.log('同步结果:', response.data)

    // 重新获取配置（更新上次同步时间）
    fetchConfig()

    // 切换到日志标签页
    activeTab.value = 'logs'

    // 刷新日志列表 - 重置到第一页显示最新数据
    pagination.value.currentPage = 1
    fetchSyncLogs()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('手动同步失败:', error)
      ElMessage.error('同步失败')
    }
  }
}



// 获取同步日志
const fetchSyncLogs = async () => {
  try {
    const params = {
      limit: pagination.value.pageSize,
      skip: (pagination.value.currentPage - 1) * pagination.value.pageSize
    }
    // 通过API调用中添加的时间戳参数，确保每次请求都获取最新数据，不使用缓存
    const response = await getADSyncLogs(params)

    // 从返回的数据结构中获取日志列表和总数
    syncLogs.value = response.data.items || []
    pagination.value.total = response.data.total || 0
  } catch (error) {
    console.error('获取同步日志失败:', error)
    ElMessage.error('获取日志失败')
    syncLogs.value = []
  }
}

// 分页大小变化
const handleSizeChange = (size: number) => {
  pagination.value.pageSize = size
  fetchSyncLogs()
}

// 分页变化
const handleCurrentChange = (page: number) => {
  pagination.value.currentPage = page
  fetchSyncLogs()
}

// 获取公司名称
const getCompanyName = (id: number | null): string => {
  if (!id) return '未知'
  const company = companies.value.find(c => c.id === id)
  return company ? company.name : '未知'
}

// 获取部门名称
const getDepartmentName = (id: number | null): string => {
  if (!id) return '未知'
  const dept = departments.value.find(d => d.id === id)
  return dept ? dept.name : '未知'
}

// 显示详情
const showDetails = (log: ADSyncLog) => {
  selectedLog.value = log
  detailsVisible.value = true

  // 添加详细调试信息
  console.log('选中的日志详情:', selectedLog.value)
  console.log('禁用用户信息路径1:', selectedLog.value?.details?.disabled_users_details)
  console.log('禁用用户信息路径2:', selectedLog.value?.details?.stats?.disabled_users_details)
  console.log('禁用用户数量:', selectedLog.value?.disabled_users)
  console.log('所有可用字段:', Object.keys(selectedLog.value?.details || {}))

  // 兼容处理：如果details中没有disabled_users_details，但stats中有，则添加进来
  if (selectedLog.value && selectedLog.value.details) {
    if (!selectedLog.value.details.disabled_users_details &&
        selectedLog.value.details.stats &&
        selectedLog.value.details.stats.disabled_users_details) {
      // 将stats中的数据复制到details根级别，以便模板渲染
      selectedLog.value.details.disabled_users_details = selectedLog.value.details.stats.disabled_users_details
      console.log('已从stats中复制禁用用户数据到details根级别')
    }
  }
}

// 处理同步时间变更
const handleSyncTimeChange = (time: Date | null) => {
  if (time) {
    // 格式化为HH:MM格式
    const hours = time.getHours().toString().padStart(2, '0')
    const minutes = time.getMinutes().toString().padStart(2, '0')
    configForm.value.sync_time = `${hours}:${minutes}`
  } else {
    configForm.value.sync_time = null
  }
}

// 导出用户密码
const handleExportPasswords = async (format: 'xlsx' | 'csv') => {
  if (!selectedLog.value || !selectedLog.value.id) {
    ElMessage.warning('没有可导出的用户密码')
    return
  }

  try {
    ElMessage.info('正在生成密码文件，请稍候...')

    const response = await exportSyncLogPasswords(selectedLog.value.id, format)

    // 创建Blob对象
    const blob = new Blob([response.data], {
      type: format === 'xlsx'
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'text/csv'
    })

    // 默认文件名
    let filename = `AD用户密码_${selectedLog.value.sync_time ? selectedLog.value.sync_time.replace(/[- :]/g, '') : new Date().toISOString().slice(0, 19).replace(/[-:T]/g, '')}.${format}`

    // 获取文件名 - 优化文件名解析逻辑
    const contentDisposition = response.headers['content-disposition']
    if (contentDisposition) {
      // 尝试从RFC标准格式的header中提取UTF-8文件名
      const filenameUtf8Match = contentDisposition.match(/filename\*=UTF-8''([^;]+)/)
      if (filenameUtf8Match && filenameUtf8Match[1]) {
        try {
          filename = decodeURIComponent(filenameUtf8Match[1])
        } catch (e) {
          console.warn('UTF-8文件名解码失败:', e)
        }
      } else {
        // 尝试从常规格式中提取
        const regularMatch = contentDisposition.match(/filename="?([^";]+)"?/)
        if (regularMatch && regularMatch[1]) {
          filename = regularMatch[1].replace(/"/g, '')
        }
      }
    }

    // 创建下载链接
    const link = document.createElement('a')
    link.href = URL.createObjectURL(blob)
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    ElMessage.success('用户密码导出成功')
  } catch (error) {
    console.error('导出用户密码失败:', error)
    ElMessage.error('导出用户密码失败')
  }
}

// 直接从日志列表导出密码
const exportLogPasswords = (log: ADSyncLog) => {
  selectedLog.value = log
  ElMessageBox.confirm(
    '请选择导出格式',
    '导出用户密码',
    {
      confirmButtonText: 'Excel格式',
      cancelButtonText: 'CSV格式',
      type: 'info',
    }
  ).then(() => {
    // 用户点击了Excel格式
    handleExportPasswords('xlsx')
  }).catch(action => {
    if (action === 'cancel') {
      // 用户点击了CSV格式
      handleExportPasswords('csv')
    }
  })
}

// 导出完整日志
const exportFullLog = async (format: 'xlsx' | 'csv') => {
  if (!selectedLog.value || !selectedLog.value.id) {
    ElMessage.warning('无法导出日志');
    return;
  }

  try {
    ElMessage.info('正在生成日志文件，请稍候...');

    const response = await exportSyncLogFullDetails(selectedLog.value.id, format);

    // 创建Blob对象
    const blob = new Blob([response.data], {
      type: format === 'xlsx'
        ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        : 'application/zip' // CSV导出实际是一个ZIP文件
    });

    // 默认文件名
    let filename = `AD同步日志_${selectedLog.value.sync_time ? selectedLog.value.sync_time.replace(/[- :]/g, '') : new Date().toISOString().slice(0, 19).replace(/[-:T]/g, '')}.${format === 'xlsx' ? 'xlsx' : 'zip'}`;

    // 获取文件名 - 优化的文件名解析逻辑，与handleExportPasswords保持一致
    const contentDisposition = response.headers['content-disposition'];
    if (contentDisposition) {
      // 尝试从RFC标准格式的header中提取UTF-8文件名
      const filenameUtf8Match = contentDisposition.match(/filename\*=UTF-8''([^;]+)/);
      if (filenameUtf8Match && filenameUtf8Match[1]) {
        try {
          filename = decodeURIComponent(filenameUtf8Match[1]);
        } catch (e) {
          console.warn('UTF-8文件名解码失败:', e);
        }
      } else {
        // 尝试从常规格式中提取
        const regularMatch = contentDisposition.match(/filename="?([^";]+)"?/);
        if (regularMatch && regularMatch[1]) {
          filename = regularMatch[1].replace(/"/g, '');
        }
      }
    }

    // 创建下载链接
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ElMessage.success('日志导出成功');
  } catch (error) {
    console.error('导出日志失败:', error);
    ElMessage.error('导出日志失败');
  }
}

onMounted(() => {
  // 获取配置和数据
  fetchConfig()
  fetchOUTree()
  fetchCompanies()
  fetchDepartments()
  fetchSyncLogs()
})
</script>

<style scoped>
.ad-sync-config {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.title-area {
  display: flex;
  align-items: center;
}

.header-icon {
  font-size: 24px;
  margin-right: 8px;
  color: var(--el-color-primary);
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.box-card {
  margin-bottom: 20px;
}

.tabs-container {
  height: 100%;
}

.target-ou-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.config-card, .log-card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}

.pagination-container {
  margin-top: 20px;
}

.form-actions {
  text-align: right;
  margin-top: 20px;
}

.error-item {
  color: #f56c6c;
  padding: 5px 0;
  border-bottom: 1px dashed #ebeef5;
}

.error-item:last-child {
  border-bottom: none;
}

.actions-container {
  margin: 10px 0;
  display: flex;
  gap: 10px;
}

/* 表格内容样式 */
:deep(.el-table .cell) {
  padding: 0 5px;
}

:deep(.el-table--striped .el-table__body tr.el-table__row--striped td) {
  background-color: #fafafa;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover>td) {
  background-color: #f5f7fa;
}

:deep(.el-table__row) {
  height: 48px;
}

:deep(.el-table__header th) {
  padding: 8px 0;
}

/* 添加图标与文字的间距 */
.mr-1 {
  margin-right: 5px;
}

/* 详情对话框样式 */
h3 {
  font-size: 16px;
  margin: 20px 0 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

/* 信息图标样式 */
.info-icon {
  margin-left: 5px;
  font-size: 16px;
  color: #909399;
  cursor: pointer;
}

.info-icon:hover {
  color: #409eff;
}

/* 对话框底部按钮样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 字段变更样式 */
.field-change {
  margin-bottom: 5px;
  padding: 3px 0;
  border-bottom: 1px dashed #eee;
}

.field-change:last-child {
  border-bottom: none;
}

.old-value {
  color: #f56c6c;
  text-decoration: line-through;
  margin: 0 5px;
}

.arrow {
  color: #909399;
  margin: 0 5px;
}

.new-value {
  color: #67c23a;
  font-weight: bold;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .pagination-container {
    justify-content: center;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .card-header > span {
    margin-bottom: 10px;
  }
}
</style>