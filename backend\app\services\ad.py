from typing import List, Optional, Dict, Any, Union, Tuple
import logging
import os
import time
import string
import random
import asyncio
from fastapi import HTTPException, status, UploadFile, File, Form
from fastapi.encoders import jsonable_encoder
from datetime import datetime, timedelta
import json
import csv
import io
import pandas as pd
from fastapi.responses import StreamingResponse
import openpyxl
from openpyxl.worksheet.datavalidation import DataValidation
import re
import traceback
import tempfile
import ldap3
from ldap3 import SUBTREE, MODIFY_REPLACE

from app import schemas, models, crud
from app.utils.ad_client import ADClient, ad_client, extract_dept_id_from_group
from app.schemas.ad import ADUserCreate, ADUserUpdate
from app.api.v1.ecology import EcologyUser as EcologySchema  # 导入正确的EcologyUser模型

# 配置日志
logger = logging.getLogger(__name__)

# 初始化AD客户端
ad_client = ADClient()

async def get_ou_tree() -> List[dict]:
    """获取完整的OU树结构"""
    try:
        ou_tree = await ad_client.get_ou_tree()
        if not ou_tree:
            logger.warning("从AD获取OU树为空")
            return []

        # 统计根级OU数量和总OU数量，而不是打印完整数据
        total_ous = count_all_ous(ou_tree)
        logger.info(f"获取到OU树数据: 根级OU数量={len(ou_tree)}, 总OU数量={total_ous}")
        return ou_tree
    except Exception as e:
        logger.error(f"获取OU树失败: {str(e)}")
        return []

def count_all_ous(ou_tree: List[dict]) -> int:
    """统计OU树中的总OU数量"""
    count = 0

    def count_children(ous):
        nonlocal count
        for ou in ous:
            count += 1
            if 'children' in ou and ou['children']:
                count_children(ou['children'])

    count_children(ou_tree)
    return count

async def create_ou(ou_data: schemas.OUCreate) -> schemas.OUResponse:
    """创建组织单位"""
    try:
        success = await ad_client.create_ou(
            parent_dn=ou_data.parent_dn,
            name=ou_data.name,
            description=ou_data.description
        )
        if not success:
            error_msg = "创建OU失败"
            if hasattr(ad_client, '_conn') and hasattr(ad_client._conn, 'result'):
                result = ad_client._conn.result
                if result.get('description') == 'entryAlreadyExists':
                    error_msg = f"OU '{ou_data.name}' 已存在"
                elif result.get('message'):
                    error_msg = f"创建OU失败: {result.get('message')}"
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )

        # 构建新OU的DN
        new_ou_dn = f"OU={ou_data.name},{ou_data.parent_dn}"

        # 返回符合OUResponse schema的数据
        return schemas.OUResponse(
            name=ou_data.name,
            description=ou_data.description,
            dn=new_ou_dn,
            parent_dn=ou_data.parent_dn,
            children=[]
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建OU失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建OU失败: {str(e)}"
        )

async def get_users(ou_dn: str, page: int, page_size: int, search: str = None):
    """获取用户列表(分页)"""
    try:
        logger.info(f"开始获取用户列表: ou_dn={ou_dn}, page={page}, page_size={page_size}, search={search}")

        # 获取用户数据和总数
        users, total = await ad_client.get_users(ou_dn, page, page_size, search)

        # 只记录数量和简单结构，而不是完整数据
        if users:
            user_sample = users[0]['username'] if users else ''
            logger.debug(f"AD返回用户数据: 页面数量={len(users)}, 总数={total}, 示例用户={user_sample}")
        else:
            logger.warning(f"AD未返回用户数据: ou_dn={ou_dn}")
            return {
                "items": [],
                "total": 0,
                "page": page,
                "page_size": page_size
            }

        return {
            "items": users,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        logger.error(f"获取用户列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户列表失败: {str(e)}"
        )

async def get_groups(ou_dn: str, page: int, page_size: int, search: str = None):
    """获取组列表(分页)"""
    try:
        logger.info(f"开始获取组列表: ou_dn={ou_dn}, page={page}, page_size={page_size}, search={search}")

        # 获取组数和总数
        groups, total = await ad_client.get_groups(ou_dn, page, page_size, search)

        # 只记录数量和简单结构，而不是完整数据
        if groups:
            group_sample = groups[0]['name'] if groups else ''
            logger.debug(f"AD返回组数据: 页面数量={len(groups)}, 总数={total}, 示例组={group_sample}")
        else:
            logger.warning(f"AD未返回组数据: ou_dn={ou_dn}")
            return {
                "items": [],
                "total": 0,
                "page": page,
                "page_size": page_size
            }

        return {
            "items": groups,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        logger.error(f"获取组列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取组列表失败: {str(e)}"
        )

async def create_group(group: schemas.ADGroupCreate) -> dict:
    """创建新的组"""
    try:
        success = await ad_client.create_group(
            name=group.name,
            ou_dn=group.ou_dn,
            description=group.description
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="创建组失败"
            )

        return {"message": "创建成功"}

    except Exception as e:
        logger.error(f"创建组失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建组失败: {str(e)}"
        )

async def update_group(name: str, group: schemas.ADGroupUpdate) -> dict:
    """更新组信息"""
    try:
        # 确保连接可用
        if not await ad_client.ensure_connected():
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="AD连接失败"
            )

        # 首先获取组的DN
        search_filter = f'(&(objectClass=group)(sAMAccountName={name}))'
        # 注意：_conn.search 是同步方法，不使用 await
        success = ad_client._conn.search(
            search_base=ad_client._config['search_base'],
            search_filter=search_filter,
            search_scope=SUBTREE,
            attributes=['distinguishedName']
        )

        if not success or not ad_client._conn.entries:
            logger.error(f"未找到组: {name}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"未找到组: {name}"
            )

        group_dn = ad_client._conn.entries[0].distinguishedName.value

        # 准备要更新的属性
        attributes = {}
        if group.description is not None:
            attributes['description'] = group.description

        # 准备更新的属性
        modify_attrs = {}
        if 'description' in attributes:
            modify_attrs['description'] = [(MODIFY_REPLACE, [attributes['description']])]

        # 执行更新，注意：_conn.modify 是同步方法，不使用 await
        success = ad_client._conn.modify(group_dn, modify_attrs)

        if not success:
            error_msg = ad_client._conn.result.get('description', '未知错误')
            logger.error(f"更新组失败: {error_msg}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"更新组失败: {error_msg}"
            )

        logger.info(f"组更新成功: {name}")
        return {"message": "更新成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新组失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新组失败: {str(e)}"
        )

async def delete_group(name: str) -> dict:
    """删除组"""
    try:
        success = await ad_client.delete_group(name)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除组失败"
            )

        return {"message": "删除成功"}

    except Exception as e:
        logger.error(f"删除组失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除组失败: {str(e)}"
        )

async def create_user(user: schemas.ADUserCreate) -> dict:
    """创建新的用户"""
    try:
        # 先创建用户
        success = await ad_client.create_user(user.model_dump())
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="用户创建失败"
            )

        # 用户创建成功后的后续操作
        group_errors = []

        # 如果指定了组,尝试将用户添加到组中
        if user.groups:
            group_success, _ = await ad_client.add_user_to_groups(
                user.username,
                user.groups
            )
            if not group_success:
                # 记录错误但不中断操作
                error_msg = "用户创建成功但添加到组失败"
                logger.error(error_msg)
                group_errors.append(error_msg)

        # 如果需要下次登录修改密码，设置相应的AD属性
        pwd_change_error = None
        if user.change_password_next_logon:
            pwd_change_success = await ad_client.set_user_must_change_password(user.username)
            if not pwd_change_success:
                error_msg = "设置用户下次登录修改密码失败"
                logger.error(error_msg)
                pwd_change_error = error_msg

        # 构建响应
        response = {"message": "用户创建成功"}

        # 如果有警告或非致命错误，添加到响应中
        warnings = []
        if group_errors:
            warnings.append("用户组关联失败")
        if pwd_change_error:
            warnings.append(pwd_change_error)

        if warnings:
            response["warnings"] = warnings

        return response

    except ValueError as e:
        logger.error(f"创建用户参数错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        # 直接重新抛出HTTPException
        raise
    except Exception as e:
        logger.error(f"创建用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建用户失败: {str(e)}"
        )

async def update_user(username: str, user_update: schemas.ADUserUpdate) -> dict:
    """更新用户信息"""
    try:
        update_data = user_update.model_dump(exclude_unset=True)
        groups = update_data.pop('groups', None)

        # 更新用户基本信息
        if update_data:
            success = await ad_client.update_user(username, update_data)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="更新用户信息失败"
                )

        # 更新用户组
        if groups is not None:
            # 获取用户当前的组
            current_groups = await ad_client.get_user_groups(username)
            # 需要添加的组
            groups_to_add = set(groups) - set(current_groups)
            # 需要移除的组
            groups_to_remove = set(current_groups) - set(groups)

            if groups_to_add:
                success = await ad_client.add_user_to_groups(username, list(groups_to_add))
                if not success:
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="添加用户到新组失败"
                    )

            if groups_to_remove:
                success = await ad_client.remove_user_from_groups(username, list(groups_to_remove))
                if not success:
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="从组中移除用户失败"
                    )

        return {"message": "用户更新成功"}
    except ValueError as e:
        logger.error(f"更新用户参数错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        # 直接重新抛出HTTPException
        raise
    except Exception as e:
        logger.error(f"更新用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户失败: {str(e)}"
        )

async def toggle_user_status(username: str) -> dict:
    """切换用户状态（启用/禁用）"""
    try:
        success = await ad_client.toggle_user_status(username)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="切换用户状态失败"
            )
        return {"message": "状态切换成功"}
    except ValueError as e:
        logger.error(f"切换用户状态参数错误: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        # 直接重新抛出HTTPException
        raise
    except Exception as e:
        logger.error(f"切换用户状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"切换用户状态失败: {str(e)}"
        )

async def update_ou(ou_dn: str, attributes: Dict[str, Any]) -> schemas.OUResponse:
    """更新OU属性"""
    try:
        # 验证OU是否存在
        parent_dn = ou_dn.split(',', 1)[1] if ',' in ou_dn else None
        if not parent_dn:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的OU DN，必须包含父OU"
            )

        # 如果需要重命名OU
        if "name" in attributes and attributes["name"]:
            new_name = attributes["name"]
            current_name = ou_dn.split(',')[0].split('=')[1]

            if new_name != current_name:
                # 检查新名称是否已被使用
                escaped_name = ad_client.escape_dn_for_filter(attributes["name"])
                search_filter = f'(&(objectClass=organizationalUnit)(ou={escaped_name}))'
                existing_ous = await ad_client.search(
                    search_base=parent_dn,
                    search_filter=search_filter,
                    search_scope="one"
                )

                if existing_ous:
                    for existing_ou in existing_ous:
                        if existing_ou['dn'] != ou_dn:  # 排除OU自身
                            raise HTTPException(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                detail=f"同级OU中已存在同名OU: {new_name}"
                            )

                # 重命名OU
                success = await ad_client.rename_ou(ou_dn, new_name)
                if not success:
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="重命名OU失败"
                    )

                # 更新OU DN，因为它已被重命名
                ou_dn = f"OU={new_name},{parent_dn}"

        success = await ad_client.update_ou(ou_dn, attributes)
        if not success:
            error_msg = "更新OU失败"
            if hasattr(ad_client, '_conn') and hasattr(ad_client._conn, 'result'):
                result = ad_client._conn.result
                if result.get('message'):
                    error_msg = f"更新OU失败: {result.get('message')}"
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )

        # 构建新的DN（如果名称已更改）
        new_dn = ou_dn
        if 'name' in attributes:
            parent_dn = ','.join(ou_dn.split(',')[1:])
            new_dn = f"OU={attributes['name']},{parent_dn}"

        # 返回更新后的OU信息
        return schemas.OUResponse(
            name=attributes.get('name', ou_dn.split(',')[0].split('=')[1]),
            description=attributes.get('description', ''),
            dn=new_dn,
            children=[]
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新OU失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新OU失败: {str(e)}"
        )

async def delete_ou(ou_dn: str) -> dict:
    """删除OU"""
    try:
        success = await ad_client.delete_ou(ou_dn)
        if not success:
            error_msg = "删除OU失败"
            if hasattr(ad_client, '_conn') and hasattr(ad_client._conn, 'result'):
                result = ad_client._conn.result
                if result.get('message'):
                    error_msg = f"删除OU失败: {result.get('message')}"
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        return {"message": "删除成功"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除OU失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除OU失败: {str(e)}"
        )

async def get_user_groups(username: str) -> List[str]:
    """获取用户所属的组"""
    try:
        groups = await ad_client.get_user_groups(username)
        return groups
    except Exception as e:
        logger.error(f"获取用户组失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户组失败: {str(e)}"
        )

async def update_user_groups(username: str, new_groups: List[str], append_mode: bool = False) -> dict:
    """更新用户所属的组

    Args:
        username: 用户名
        new_groups: 新的组列表
        append_mode: 是否为追加模式，True表示追加，False表示覆盖
    """
    try:
        # 获取用户当前的组
        try:
            current_groups = await ad_client.get_user_groups(username)
            logger.info(f"用户 {username} 当前所属组: {len(current_groups)} 个")
        except Exception as e:
            logger.error(f"获取用户组失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取用户组失败: {str(e)}"
            )

        # 记录操作结果
        results = {
            "success": True,
            "added_groups": [],
            "removed_groups": [],
            "errors": []
        }

        if append_mode:
            # 追加模式：合并现有组和新组
            target_groups = list(set(current_groups) | set(new_groups))  # 使用集合合并去重
            groups_to_add = list(set(new_groups) - set(current_groups))
            groups_to_remove = []

            # 只添加新组
            if groups_to_add:
                try:
                    add_success, _ = await ad_client.add_user_to_groups(username, groups_to_add)
                    if add_success:
                        results["added_groups"] = groups_to_add
                        logger.info(f"成功添加用户 {username} 到 {len(groups_to_add)} 个组")
                    else:
                        results["success"] = False
                        results["errors"].append("添加用户到新组失败")
                        logger.error(f"添加用户 {username} 到组失败")
                except Exception as e:
                    results["success"] = False
                    results["errors"].append(f"添加组时出错: {str(e)}")
                    logger.error(f"添加用户到组时出错: {str(e)}")
        else:
            # 覆盖模式：完全替换现有组
            target_groups = new_groups
            groups_to_add = list(set(new_groups) - set(current_groups))
            groups_to_remove = list(set(current_groups) - set(new_groups))

            # 添加新组
            if groups_to_add:
                try:
                    add_success, _ = await ad_client.add_user_to_groups(username, groups_to_add)
                    if add_success:
                        results["added_groups"] = groups_to_add
                        logger.info(f"成功添加用户 {username} 到 {len(groups_to_add)} 个组")
                    else:
                        results["success"] = False
                        results["errors"].append("添加用户到新组失败")
                        logger.error(f"添加用户 {username} 到组失败")
                except Exception as e:
                    results["success"] = False
                    results["errors"].append(f"添加组时出错: {str(e)}")
                    logger.error(f"添加用户到组时出错: {str(e)}")

            # 移除旧组
            if groups_to_remove:
                try:
                    remove_success = await ad_client.remove_user_from_groups(username, groups_to_remove)
                    if remove_success:
                        results["removed_groups"] = groups_to_remove
                        logger.info(f"成功从 {len(groups_to_remove)} 个组中移除用户 {username}")
                    else:
                        results["success"] = False
                        results["errors"].append("从组中移除用户失败")
                        logger.error(f"从组中移除用户 {username} 失败")
                except Exception as e:
                    results["success"] = False
                    results["errors"].append(f"移除组时出错: {str(e)}")
                    logger.error(f"从组中移除用户时出错: {str(e)}")

        # 构建响应
        response = {"message": "更新用户组成功"}
        if not results["success"]:
            response["message"] = "更新用户组部分失败"
            response["errors"] = results["errors"]

        response["details"] = {
            "added_groups": results["added_groups"],
            "removed_groups": results["removed_groups"],
            "total_groups": len(target_groups)
        }

        return response

    except Exception as e:
        logger.error(f"更新用户组失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新用户组失败: {str(e)}"
        )

async def get_all_groups(ou_dn: str = None) -> List[Dict]:
    """获取所有组列表"""
    try:
        logger.info("开始获取所有组列表")
        groups = await ad_client.get_all_groups(None)  # 传入None表示获取所有组

        if not groups:
            logger.warning("AD未返回组数据")
            return []

        return groups
    except Exception as e:
        logger.error(f"获取组列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取组列表失败: {str(e)}"
        )

async def get_group_members(group_name: str, page: int = 1, page_size: int = 20, search: str = None):
    """获取组成员列表(分页)

    Args:
        group_name: 组名称
        page: 页码
        page_size: 每页数量
        search: 搜索关键词，支持按用户名、显示名称、部门、职位搜索
    """
    try:
        logger.info(f"开始获取组成员: group_name={group_name}, page={page}, page_size={page_size}, search={search}")

        # 获取成员列表和总数
        members, total = await ad_client.get_group_members(group_name, page, page_size, search)

        # 只记录数量和简单统计信息
        if members:
            member_sample = members[0]['username'] if members else ''
            logger.debug(f"AD返回组成员数据: 页面数量={len(members)}, 总数={total}, 示例成员={member_sample}")
            if search:
                logger.info(f"搜索结果: 关键词='{search}', 匹配数={total}")
        else:
            logger.warning(f"AD未返回成员数: group_name={group_name}")
            return {
                "items": [],
                "total": 0,
                "page": page,
                "page_size": page_size
            }

        return {
            "items": members,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        logger.error(f"获取组成员列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取组成员列表失败: {str(e)}"
        )

async def add_group_members(name: str, members: List[str]) -> dict:
    """添加组成员"""
    try:
        # 获取组DN
        group_dn = await ad_client.get_group_dn(name)
        if not group_dn:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="组不存在"
            )

        # 添加成员
        success = await ad_client.add_members_to_group(group_dn, members)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="添加成员失败"
            )

        return {"message": "添加成员成功"}

    except Exception as e:
        logger.error(f"添加组成员失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加组成员失败: {str(e)}"
        )

async def remove_group_members(name: str, members: List[str]) -> dict:
    """删除成员"""
    try:
        # 获取组DN
        group_dn = await ad_client.get_group_dn(name)
        if not group_dn:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="组不存在"
            )

        # 删除成员
        success = await ad_client.remove_members_from_group(group_dn, members)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除成员失败"
            )

        return {"message": "删除成员成功"}

    except Exception as e:
        logger.error(f"删除组成员失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除组成员失败: {str(e)}"
        )

async def search_users(search: str = None, page: int = 1, page_size: int = 20):
    """全局搜索用户"""
    try:
        logger.info(f"开始全局搜索用户: search={search}, page={page}, page_size={page_size}")

        # 获取用户数据和总数
        users, total = await ad_client.search_users(search, page, page_size)

        # 只记录数量和关键信息
        if users:
            user_sample = users[0]['username'] if users else ''
            logger.debug(f"AD返回搜索结果: 页面数量={len(users)}, 总数={total}, 示例用户={user_sample}")
        else:
            logger.debug(f"AD搜索未返回结果: search={search}")

        return {
            "items": users,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        logger.error(f"全局搜索用户失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索用户失败: {str(e)}"
        )

async def parse_csv_users(content: bytes) -> List[Dict]:
    """解析CSV文件内容"""
    try:
        logger.info("开始解析CSV文件")
        # 尝试不同的编码
        encodings = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']
        text_content = None

        for encoding in encodings:
            try:
                text_content = content.decode(encoding)
                logger.info(f"成功使用 {encoding} 编码解析CSV文件")
                break
            except UnicodeDecodeError:
                logger.warning(f"使用 {encoding} 解码失败,尝试下一个编码")
                continue

        if text_content is None:
            raise ValueError("无法解析文件编码，请确保文件使用UTF-8或GBK编码保存")

        reader = csv.DictReader(io.StringIO(text_content))
        logger.info(f"CSV文件头: {reader.fieldnames}")
        users = []
        row_num = 0

        def safe_get(row, field, default=''):
            """安全获取字段值,处理None和空字符串"""
            logger.debug(f"获取字段 {field} 的值")
            value = row.get(field)
            logger.debug(f"原始值: {value}, 类型: {type(value)}")

            # 先检查是否为None或空字符串
            if value is None or str(value).strip() == '':
                logger.debug(f"字段 {field} 值为空,返回默认值: {default}")
                return default

            # 转换为字符串并去除空格
            try:
                str_value = str(value).strip()
                logger.debug(f"处理后的值: {str_value}")
                return str_value
            except Exception as e:
                logger.error(f"转换字段 {field} 值时出错: {str(e)}")
                return default

        for row in reader:
            row_num += 1
            logger.info(f"正在处理第 {row_num} 行数据")
            logger.debug(f"原始行数据: {row}")

            try:
                # 处理必填字段
                username = safe_get(row, 'username')
                name = safe_get(row, 'name')
                logger.info(f"处理用户: username={username}, name={name}")

                if not username or not name:
                    error_msg = f"用户名和显示名称为必填字段 (username={username}, name={name})"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                # 处理密码
                password = safe_get(row, 'password')
                if not password:
                    password = generate_random_password()
                    logger.info(f"为用户 {username} 生成随机密码")

                # 处理组列表
                groups = safe_get(row, 'groups')
                if groups:
                    groups = [g.strip() for g in groups.split(';') if g.strip()]
                    logger.info(f"用户 {username} 的组列表: {groups}")
                else:
                    groups = []
                    logger.info(f"用户 {username} 没有指定组")

                # 处理change_password_next_logon字段
                change_password = safe_get(row, 'change_password_next_logon').upper()
                change_password = change_password in ['TRUE', 'YES', '1', 'Y']
                logger.info(f"用户 {username} 下次登录是否需要修改密码: {change_password}")

                user_data = {
                    'username': username,
                    'name': name,
                    'email': safe_get(row, 'email'),
                    'department': safe_get(row, 'department'),
                    'title': safe_get(row, 'title'),
                    'password': password,
                    'ou_dn': safe_get(row, 'ou_dn'),
                    'groups': groups,
                    'change_password_next_logon': change_password
                }
                logger.debug(f"处理后的用户数据: {user_data}")
                users.append(user_data)
                logger.info(f"成功处理用户 {username}")

            except Exception as e:
                error_msg = f"处理第 {row_num} 行数据失败: {str(e)}, 行数据: {row}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        logger.info(f"CSV文件解析完成,共处理 {len(users)} 个用户")
        return users

    except Exception as e:
        error_msg = f"解析CSV文件失败: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )

async def parse_excel_users(content: bytes) -> List[Dict]:
    """解析Excel文件"""
    try:
        logger.info("开始解析Excel文件")
        df = pd.read_excel(io.BytesIO(content))
        logger.info(f"Excel文件列名: {df.columns.tolist()}")
        logger.info(f"总行数: {len(df)}")
        users = []

        for index, row in df.iterrows():
            logger.info(f"正在处理第 {index + 1} 行数据")
            logger.debug(f"原始行数据: {row.to_dict()}")

            try:
                # 安全地获取字段值,避免None值问题
                def safe_get(field, default=''):
                    """安全获取字段值,处理None和空值"""
                    logger.debug(f"获取字段 {field} 的值")
                    value = row.get(field)
                    logger.debug(f"原始值: {value}, 类型: {type(value)}")

                    # 检查是否为pandas的空值
                    if pd.isna(value) or value is None:
                        logger.debug(f"字段 {field} 值为空,返回默认值: {default}")
                        return default

                    # 转换为字符串并去除空格
                    try:
                        str_value = str(value).strip()
                        logger.debug(f"处理后的值: {str_value}")
                    except Exception as e:
                        logger.error(f"转换字段 {field} 值时出错: {str(e)}")
                        return default

                    # 检查是否为空字符串
                    if str_value == '':
                        logger.debug(f"字段 {field} 为空字符串,返回默认值: {default}")
                        return default

                    return str_value

                # 处理必填字段
                username = safe_get('username')
                name = safe_get('name')
                logger.info(f"处理用户: username={username}, name={name}")

                if not username or not name:
                    error_msg = f"用户名和显示名称为必填字段 (username={username}, name={name})"
                    logger.error(error_msg)
                    raise ValueError(error_msg)

                # 处理密码
                password = safe_get('password')
                if not password:
                    password = generate_random_password()
                    logger.info(f"为用户 {username} 生成随机密码")

                # 处理组列表
                groups = safe_get('groups')
                if groups:
                    groups = [g.strip() for g in groups.split(';') if g.strip()]
                    logger.info(f"用户 {username} 的组列表: {groups}")
                else:
                    groups = []
                    logger.info(f"用户 {username} 没有指定组")

                # 处理change_password_next_logon字段
                change_password = safe_get('change_password_next_logon').upper()
                change_password = change_password in ['TRUE', 'YES', '1', 'Y']
                logger.info(f"用户 {username} 下次登录是否需要修改密码: {change_password}")

                user = {
                    'username': username,
                    'name': name,
                    'email': safe_get('email'),
                    'department': safe_get('department'),
                    'title': safe_get('title'),
                    'password': password,
                    'ou_dn': safe_get('ou_dn'),
                    'groups': groups,
                    'change_password_next_logon': change_password
                }

                logger.debug(f"处理后的用户数据: {user}")
                users.append(user)
                logger.info(f"成功处理用户 {username}")

            except Exception as e:
                error_msg = f"处理第 {index + 1} 行数据失败: {str(e)}, 行数据: {row.to_dict()}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        logger.info(f"Excel文件解析完成,共处理 {len(users)} 个用户")
        return users

    except Exception as e:
        error_msg = f"解析Excel文件失败: {str(e)}"
        logger.error(error_msg, exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )

async def find_ou_dn(ou_name: str, ou_tree: List[dict]) -> Optional[str]:
    """根据OU名称在整个OU树中查找DN"""
    if not ou_name:
        return None

    logger.info(f"开始查找OU: {ou_name}")

    # 存储所有匹配的OU
    matched_ous = []

    def search_ou(ous, target_name, current_path=""):
        """递归搜索OU，记录完整路径"""
        for ou in ous:
            # 构建当前OU的完整路径
            path = f"{current_path}/{ou['name']}" if current_path else ou['name']

            if ou['name'].lower() == target_name.lower():
                matched_ous.append({
                    'dn': ou['dn'],
                    'path': path
                })

            # 递归搜索子OU
            if ou.get('children'):
                search_ou(ou['children'], target_name, path)

    # 执行搜索
    search_ou(ou_tree, ou_name)

    # 根据匹配结果处理
    if not matched_ous:
        return None
    elif len(matched_ous) == 1:
        # 只有一个匹配，直接返回
        logger.info(f"找到唯一匹配的OU: {matched_ous[0]['path']} -> {matched_ous[0]['dn']}")
        return matched_ous[0]['dn']
    else:
        # 多个匹配，抛出异常并列出所有匹配项
        paths = [ou['path'] for ou in matched_ous]
        raise ValueError(f"找到多个匹配的OU '{ou_name}'，请使用完整路径指定：\n" +
                        "\n".join(f"- {path}" for path in paths))

async def bulk_import_users(default_ou_dn: str, users: List[Dict]) -> Dict:
    """批量导入用户"""
    successful_users = []
    failed_users = []

    # 获取所有组的映射关系
    all_groups = await ad_client.get_all_groups(None)
    group_name_to_dn = {group['name']: group['dn'] for group in all_groups}

    # 获取OU树结构
    ou_tree = await get_ou_tree()
    logger.info(f"获取到的OU树结构: {ou_tree}")

    for user in users:
        try:
            # 处理OU
            ou_input = user.get('ou_dn')
            if ou_input and str(ou_input).strip():
                ou_input = str(ou_input).strip()
                # 检查是否包含完整路径
                if '/' in ou_input:
                    # 如果提供了完整路径，按径查找
                    parts = [p.strip() for p in ou_input.split('/')]
                    current_ous = ou_tree
                    current_ou = None

                    for part in parts:
                        found = False
                        for ou in current_ous:
                            if ou['name'].lower() == part.lower():
                                current_ou = ou
                                current_ous = ou.get('children', [])
                                found = True
                                break
                        if not found:
                            raise ValueError(f"找不到路径中的OU: {part}")

                    if current_ou:
                        ou_dn = current_ou['dn']
                    else:
                        raise ValueError(f"无效的OU路径: {ou_input}")
                else:
                    # 如果只提供了OU名称，尝试查找
                    try:
                        ou_dn = await find_ou_dn(ou_input, ou_tree)
                    except ValueError as e:
                        raise ValueError(str(e) + "\n或者留空使用当前选中的OU")
            else:
                ou_dn = default_ou_dn

            # 处理组名到DN转换
            group_names = user.get('groups', [])
            group_dns = []
            invalid_groups = []

            if isinstance(group_names, str):
                group_names = [g.strip() for g in group_names.split(';') if g.strip()]

            for group_name in group_names:
                if group_name in group_name_to_dn:
                    group_dns.append(group_name_to_dn[group_name])
                else:
                    invalid_groups.append(group_name)

            if invalid_groups:
                raise ValueError(f"找不到以下用户组: {', '.join(invalid_groups)}")

            # 创建用户
            user_create = schemas.ADUserCreate(
                username=user['username'],
                name=user['name'],
                email=user.get('email', ''),
                password=user.get('password', ''),
                ou_dn=ou_dn,
                department=user.get('department', ''),
                title=user.get('title', ''),
                groups=group_dns,
                change_password_next_logon=user.get('change_password_next_logon', False)
            )

            await create_user(user_create)

            # 如果需要下次登录修改密码，设置相应的AD属性
            if user.get('change_password_next_logon', False):
                await ad_client.set_user_must_change_password(user['username'])

            successful_users.append({
                'username': user['username'],
                'name': user['name'],
                'password': user['password'],
                'ou_dn': ou_input or '当前OU',  # 修改这里：使用 ou_input 而不是 ou_name
                'groups': group_names,  # 返回组名而不是DN
                'change_password_next_logon': user.get('change_password_next_logon', False)
            })
        except ValueError as e:
            # 添加更详细的错误信息
            error_msg = str(e)
            if "找不到指定的组织单位" in error_msg:
                error_msg += "。请检查OU名称是正确，或留空使用当前OU"
            elif "找不到以下用户组" in error_msg:
                error_msg += "。请检查组名是否正确，确保组已存在"

            failed_users.append({
                'username': user['username'],
                'error': error_msg
            })
        except Exception as e:
            # 添加通用错误处理
            error_msg = str(e)
            if "already exists" in error_msg.lower():
                error_msg = "用户名已存在"
            elif "password" in error_msg.lower():
                error_msg = "密码不符合复杂度要求"

            failed_users.append({
                'username': user['username'],
                'error': error_msg
            })

    return {
        'total': len(users),
        'successful': len(successful_users),
        'failed': len(failed_users),
        'successful_users': successful_users,
        'failed_users': failed_users
    }

async def generate_random_password(length: int = 10) -> str:
    """生成随机密码，包含大小写字母、数字和特殊字符"""
    chars = string.ascii_lowercase + string.ascii_uppercase + string.digits + '@#$%^&*'
    return ''.join(random.choice(chars) for _ in range(length))

async def get_import_template(file_type: str) -> StreamingResponse:
    """生成用户导入模板"""
    headers = [
        'username', 'name', 'email', 'department', 'title',
        'password', 'ou_dn', 'groups', 'change_password_next_logon'
    ]

    example_data = [
        # 示例1：基本信息，使用当前OU
        [
            'zhangsan',
            '张三',
            '<EMAIL>',
            'IT部',
            '开发工程师',
            '',  # 留空将自动生成密码
            '',  # 留空将使用当前选中的OU
            '',  # 留空表示不分配组
            'TRUE'  # 要求下次登录修改密码
        ],
        # 示例2：使用OU名称（如果没有重名OU）
        [
            'lisi',
            '李四',
            '<EMAIL>',
            '财务部',
            '会计',
            'Password123!',
            '财务组',
            '财务组',
            'FALSE'  # 不要求修改密码
        ],
        # 示例3：使用完整OU路径（处理重名OU）
        [
            'wangwu',
            '王五',
            '<EMAIL>',
            'IT部',
            '项目经理',
            'Abcd1234!',
            'IT部/开发组',
            'IT组;项目经理组',
            'FALSE'  # 不要求修改密码
        ]
    ]

    try:
        if file_type == 'csv':
            output = io.StringIO()
            writer = csv.writer(output)
            writer.writerow(headers)
            writer.writerows(example_data)

            output.seek(0)
            return StreamingResponse(
                iter([output.getvalue().encode('utf-8-sig')]),
                media_type='text/csv',
                headers={'Content-Disposition': 'attachment; filename=user_import_template.csv'}
            )

        else:  # excel
            output = io.BytesIO()

            # 创建工作簿和工作表
            workbook = openpyxl.Workbook()

            # 创建说sheet
            instructions = workbook.active
            instructions.title = '使用说明'

            # 设置说明sheet的样式
            header_style = openpyxl.styles.NamedStyle(
                name='header',
                font=openpyxl.styles.Font(bold=True, size=11),
                fill=openpyxl.styles.PatternFill(start_color='E6E6E6', end_color='E6E6E6', fill_type='solid'),
                alignment=openpyxl.styles.Alignment(horizontal='left', vertical='center', wrap_text=True)
            )

            # 添加说明内容
            instructions.append(['字段', '说明', '是否必填', '示例值'])
            instructions.append(['username', '用户登录名，只能包含字母、数字、下划线', '是', 'zhangsan, admin_user'])
            instructions.append(['name', '显示名称，建议使用中文名', '是', '张三, 李四'])
            instructions.append(['email', '邮箱地址', '否', '<EMAIL>'])
            instructions.append(['department', '部门名称', '否', 'IT部, 财务部'])
            instructions.append(['title', '职务名称', '否', '工程师, 经理'])
            instructions.append(['password', '密码，留空将自动生成随机密码。\n密码要求：至少8位，包含大小写字、数字和特殊字符', '否', 'Password123!'])
            instructions.append([
                'ou_dn',
                '组织单位。可以是:\n' +
                '1. 留空: 使用当前选中的OU\n' +
                '2. OU名称: 直接填写目标OU名称（确保没有重名OU）\n' +
                '3. 完整路径: 使用斜杠(/)分隔的完整路径（处理重名OU）',
                '否',
                '财务组, IT部/开发组'
            ])
            instructions.append([
                'groups',
                '用户组名称，多个组用英文分号(;)分隔',
                '否',
                'IT组;管理员组'
            ])
            instructions.append([
                'change_password_next_logon',
                '是否要求用户下次登录时修改密码',
                '否',
                'TRUE/FALSE，留空默认为FALSE'
            ])

            # 设置说明sheet的列宽
            instructions.column_dimensions['A'].width = 15
            instructions.column_dimensions['B'].width = 50
            instructions.column_dimensions['C'].width = 10
            instructions.column_dimensions['D'].width = 30

            # 应用样式
            for row in instructions.iter_rows(min_row=1, max_row=1):
                for cell in row:
                    cell.style = header_style

            # 设置所有单元格自动换行和垂直居中
            for row in instructions.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = openpyxl.styles.Alignment(wrap_text=True, vertical='center')

            # 设置行高
            for row in instructions.rows:
                instructions.row_dimensions[row[0].row].height = 30

            # 添加注意事项
            instructions.append([])
            instructions.append(['注意事项：'])
            note_style = openpyxl.styles.NamedStyle(
                name='note',
                font=openpyxl.styles.Font(bold=True, color='FF0000'),
                alignment=openpyxl.styles.Alignment(wrap_text=True)
            )

            notes = [
                '1. 用户名和显示名称为必填项',
                '2. 密码留空时将自动生成随机密码',
                '3. OU留空时将使用当前选中的组织单位',
                '4. 如果存在重名OU，请使用完整路径（如：IT部/开发组',
                '5. 多个用户组使用英文分号(;)分隔',
                '6. 请勿修改表头，确保数据填写在正确的列中'
            ]

            for note in notes:
                row = instructions.max_row + 1
                cell = instructions.cell(row=row, column=1)
                cell.value = note
                cell.style = note_style
                instructions.merge_cells(start_row=row, start_column=1, end_row=row, end_column=4)

            # 创建模板sheet
            template = workbook.create_sheet('导入模板')

            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = template.cell(row=1, column=col)
                cell.value = header
                cell.style = header_style

            # 写入示例数据
            for row, data in enumerate(example_data, 2):
                for col, value in enumerate(data, 1):
                    cell = template.cell(row=row, column=col)
                    cell.value = value
                    cell.alignment = openpyxl.styles.Alignment(wrap_text=True, vertical='center')

            # 设置模板sheet的列宽
            column_widths = {
                'A': 15,  # username
                'B': 15,  # name
                'C': 25,  # email
                'D': 15,  # department
                'E': 15,  # title
                'F': 20,  # password
                'G': 25,  # ou_dn
                'H': 30,  # groups
                'I': 25,  # change_password_next_logon
            }

            for col, width in column_widths.items():
                template.column_dimensions[col].width = width

            # 设置行高
            template.row_dimensions[1].height = 30
            for row in range(2, len(example_data) + 2):
                template.row_dimensions[row].height = 25

            # 添加数据验证
            # 用户名验证只允许字母、数字、下划线）
            username_validation = DataValidation(
                type="custom",
                formula1='=REGEXMATCH(A2,"^[a-zA-Z0-9_]+$")',
                allow_blank=True,
                showErrorMessage=True,
                errorTitle='用户格式错误',
                error='用户名只能包含字母、数字和下划线'
            )
            template.add_data_validation(username_validation)
            username_validation.add(f'A2:A1048576')  # 应用到整列

            # 邮箱格式验证
            email_validation = DataValidation(
                type="custom",
                formula1='=OR(ISBLANK(C2),REGEXMATCH(C2,"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"))',
                allow_blank=True,
                showErrorMessage=True,
                errorTitle='邮箱格式错误',
                error='请输入有效的邮箱地址'
            )
            template.add_data_validation(email_validation)
            email_validation.add(f'C2:C1048576')

            # 为可选字段添加浅灰色背景
            optional_fill = openpyxl.styles.PatternFill(
                start_color='F5F5F5',
                end_color='F5F5F5',
                fill_type='solid'
            )

            # 为示例数据添加浅色背景
            example_fill = openpyxl.styles.PatternFill(
                start_color='F0F8FF',
                end_color='F0F8FF',
                fill_type='solid'
            )

            # 应用样式到可选字段
            for col in ['C', 'D', 'E', 'F', 'G', 'H', 'I']:  # 可选字段列
                for row in range(2, len(example_data) + 2):
                    cell = template.cell(row=row, column=ord(col) - ord('A') + 1)
                    cell.fill = optional_fill

            # 为示例数据添加背景色
            for row in range(2, len(example_data) + 2):
                for col in range(1, len(headers) + 1):
                    cell = template.cell(row=row, column=col)
                    if cell.value:  # 只为有值的单元格添加背景色
                        cell.fill = example_fill

            # 为 change_password_next_logon 列添加数据验证
            change_password_validation = DataValidation(
                type="list",
                formula1='"TRUE,FALSE,YES,NO"',
                allow_blank=True,
                showErrorMessage=True,
                errorTitle='输入值无效',
                error='请输入 TRUE/FALSE 或 YES/NO，留空默认为 FALSE'
            )
            template.add_data_validation(change_password_validation)
            change_password_validation.add(f'I2:I1048576')

            # 保存到输出流
            workbook.save(output)
            output.seek(0)

            return StreamingResponse(
                iter([output.getvalue()]),
                media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                headers={'Content-Disposition': 'attachment; filename=user_import_template.xlsx'}
            )

    except Exception as e:
        logger.error(f"生成导入模板失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成导入模板失败: {str(e)}"
        )

async def move_user(username: str, target_ou_dn: str) -> dict:
    """移动用户到指定OU"""
    try:
        # 移动用户
        success = await ad_client.move_user(username, target_ou_dn)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="移动用户失败"
            )

        return {"message": "用户移动成功"}

    except Exception as e:
        logger.error(f"移动用户失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"移动用户失败: {str(e)}"
        )

async def move_ou(ou_dn: str, target_ou_dn: str) -> dict:
    """移动OU到指定目标OU"""
    try:
        # 验证OU不能移动到自己的子OU中
        if target_ou_dn.endswith(ou_dn):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能将OU移动到其自身的子OU"
            )

        # 移动OU
        success = await ad_client.move_ou(ou_dn, target_ou_dn)

        if not success:
            error_msg = "移动OU失败"
            if hasattr(ad_client, '_conn') and hasattr(ad_client._conn, 'result'):
                result = ad_client._conn.result
                if result.get('description'):
                    error_msg = f"移动OU失败: {result.get('description')}"
                elif result.get('message'):
                    error_msg = f"移动OU失败: {result.get('message')}"

            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=error_msg
            )

        return {"message": "OU移动成功"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移动OU失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"移动OU失败: {str(e)}"
        )

async def sync_organization_structure(parent_ou_dn: str, company_id: Optional[int] = None, dept_id: Optional[int] = None):
    """从基础信息-人员信息同步组织结构到AD域，不涉及用户创建"""
    try:
        # 导入本地人员信息API
        from app.crud import ecology_user as ecology_user_crud
        from app.api.deps import get_db
        from app.database import db_context
        from app.api.v1.ecology import EcologyUser as EcologySchema

        # 初始化结果
        created_ous = {}
        stats = {
            "total_companies": 0,
            "total_departments": 0,
            "created_ous": 0,
            "errors": []
        }
        # 添加详细信息记录数组
        created_ous = []
        renamed_ous = []

        # 从本地数据库获取所有用户数据（用于提取公司和部门信息）
        with db_context() as db:
            # 构建筛选条件
            keyword = None
            exact_match = False
            limit = 100000  # 大数值以获取所有用户
            
            # 获取所有用户数据
            all_users_db = ecology_user_crud.get_ecology_users(db, skip=0, limit=limit, keyword=keyword, exact_match=exact_match)
            
            # 转换为同样的数据结构
            all_users = []
            for user in all_users_db:
                # 创建兼容的数据结构
                converted_user = EcologySchema(
                    DeptID=user.dept_id,
                    DeptName=user.dept_name,
                    DeptHierarchy=user.dept_hierarchy,
                    Level=user.level,
                    DeptPath=user.dept_path,
                    CompanyID=user.company_id,
                    CompanyName=user.company_name,
                    UserID=user.user_id,
                    UserName=user.user_name,
                    JobNumber=user.job_number,
                    Mobile=user.mobile,
                    Email=user.email,
                    JobTitle=user.job_title,
                    JobTitleName=user.job_title_name,
                    Gender=user.gender,
                    Status=user.status
                )
                all_users.append(converted_user)

        # 根据条件筛选用户
        if company_id:
            all_users = [user for user in all_users if user.CompanyID == company_id]
        elif dept_id:
            # 在本地数据中获取该部门及其子部门的用户
            # 先找出该部门下的直接用户
            direct_dept_users = [user for user in all_users if user.DeptID == dept_id]
            
            # 然后找出所有包含该部门ID路径的用户，这些是子部门的用户
            child_dept_users = [user for user in all_users if 
                            user.DeptPath and 
                            (f",{dept_id}," in f",{user.DeptPath}," or  # 在中间
                             user.DeptPath.startswith(f"{dept_id},") or  # 在开头
                             user.DeptPath.endswith(f",{dept_id}") or   # 在结尾
                             user.DeptPath == str(dept_id))]  # 就是该部门
            
            # 合并并去重
            all_users = list({user.JobNumber: user for user in direct_dept_users + child_dept_users if user.JobNumber}.values())
            logger.info(f"从部门ID {dept_id} 及其所有子部门筛选出 {len(all_users)} 个用户")

        # 筛选在职人员
        in_service_statuses = ['试用', '正式', '临时', '试用延期']
        in_service_users = [user for user in all_users if user.Status in in_service_statuses]
        logger.info(f"从 {len(all_users)} 个用户中筛选出 {len(in_service_users)} 个在职用户")

        # 如果没有在职用户，记录日志并提前返回
        if not in_service_users:
            logger.warning("未找到任何在职用户，无法创建组织结构")
            return {
                "message": "未找到任何在职用户，无法创建组织结构",
                "stats": {
                    "total_companies": 0,
                    "total_departments": 0,
                    "created_ous": 0,
                    "errors": ["未找到任何在职用户"]
                },
                "created_ous": {},
                "created_ous": [],
                "renamed_ous": []
            }

        # 公司和部门的OU缓存，避免重复创建
        created_ous = {}

        # 设置更长的连接超时时间
        await ad_client.set_connection_timeout(30)  # 30秒超时

        # 收集所有在职人员所在的公司信息和部门信息，包括完整部门路径
        companies = {}
        departments = {}  # 所有需要创建的部门
        department_hierarchies = {}

        # 首先收集所有在职用户的公司信息
        for user in in_service_users:
            if user.CompanyID and user.CompanyName:
                if user.CompanyID not in companies:
                    companies[user.CompanyID] = user.CompanyName

        # 然后收集所有在职用户所在部门及其上级部门的信息
        for user in in_service_users:
            if user.DeptID and user.DeptName and user.DeptPath and user.DeptHierarchy:
                # 分析部门路径，确保路径中所有部门都被收集
                dept_path_ids = user.DeptPath.split(',')
                dept_hierarchy_names = user.DeptHierarchy.split(' > ')

                # 确保路径和层级名称长度匹配
                if len(dept_path_ids) != len(dept_hierarchy_names):
                    logger.warning(f"用户 {user.UserName} 的部门路径与层级名称不匹配: {user.DeptPath} vs {user.DeptHierarchy}")
                    continue

                # 遍历部门路径，收集每一级部门信息
                for i in range(len(dept_path_ids)):
                    try:
                        current_dept_id = int(dept_path_ids[i])
                        current_dept_name = dept_hierarchy_names[i]

                        # 更新部门信息
                        if current_dept_id not in departments:
                            departments[current_dept_id] = current_dept_name

                            # 构建当前部门的路径和层级
                            current_path = ','.join(dept_path_ids[:i+1])
                            current_hierarchy = ' > '.join(dept_hierarchy_names[:i+1])

                            department_hierarchies[current_dept_id] = {
                                'path': current_path,
                                'hierarchy': current_hierarchy
                            }
                    except ValueError as e:
                        logger.error(f"处理部门ID出错: {dept_path_ids[i]} - {str(e)}")
                        continue

        stats["total_companies"] = len(companies)
        stats["total_departments"] = len(departments)

        logger.info(f"收集到 {len(companies)} 个公司和 {len(departments)} 个部门(包括上级部门)")

        # 先创建所有公司OU
        for company_id, company_name in companies.items():
            company_key = f"company_{company_id}"
            if company_key not in created_ous:
                # 检查OU是否已存在，使用自定义属性查找
                # 首先尝试使用ID查找，这样可以在公司更名时找到对应OU
                company_search_by_id = await ad_client.search(
                    search_base=parent_ou_dn,
                    search_filter=f"(&(objectClass=organizationalUnit)(description=*公司ID:{company_id}*))",
                    search_scope="one"
                )

                if company_search_by_id:
                    # 公司OU已存在，检查名称是否需要更新
                    company_ou_dn = company_search_by_id[0]['dn']
                    existing_ou_name = company_search_by_id[0]['attributes']['ou'][0]

                    if existing_ou_name != company_name:
                        # 公司名称已变更，需要重命名OU
                        try:
                            # 重命名OU
                            await ad_client.rename_ou(
                                dn=company_ou_dn,
                                new_name=company_name
                            )
                            # 更新描述中包含的名称
                            await ad_client.modify_ou(
                                dn=f"OU={company_name},{parent_ou_dn}",
                                attributes={
                                    'description': [f"公司: {company_name} (公司ID:{company_id})"]
                                }
                            )
                            company_ou_dn = f"OU={company_name},{parent_ou_dn}"
                            stats["renamed_ous"] = stats.get("renamed_ous", 0) + 1
                            logger.info(f"公司OU重命名成功: {existing_ou_name} -> {company_name}")
                            # 记录重命名的OU详细信息
                            renamed_ous.append({
                                "old_name": existing_ou_name,
                                "new_name": company_name,
                                "dn": company_ou_dn,
                                "type": "公司",
                                "id": company_id
                            })
                        except Exception as e:
                            logger.error(f"重命名公司OU失败 {existing_ou_name} -> {company_name}: {str(e)}")
                            stats["errors"].append(f"重命名公司OU失败: {str(e)}")
                            # 如果重命名失败，使用原始DN
                            company_ou_dn = company_search_by_id[0]['dn']
                    else:
                        # 公司名称未变更，使用已存在的OU
                        stats["skipped_ous"] = stats.get("skipped_ous", 0) + 1
                else:
                    # 尝试通过名称查找
                    escaped_company_name = ad_client.escape_dn_for_filter(company_name)
                    company_search_by_name = await ad_client.search(
                        search_base=parent_ou_dn,
                        search_filter=f"(&(objectClass=organizationalUnit)(ou={escaped_company_name}))",
                        search_scope="one"
                    )

                    if company_search_by_name:
                        # 如果找到同名OU，更新其描述以包含公司ID
                        company_ou_dn = company_search_by_name[0]['dn']
                        await ad_client.modify_ou(
                            dn=company_ou_dn,
                            attributes={
                                'description': [f"公司: {company_name} (公司ID:{company_id})"]
                            }
                        )
                        stats["skipped_ous"] = stats.get("skipped_ous", 0) + 1
                    else:
                        # 创建新公司OU，描述中包含公司ID以便将来查找
                        await ad_client.create_ou(
                            parent_dn=parent_ou_dn,
                            name=company_name,
                            description=f"公司: {company_name} (公司ID:{company_id})"
                        )
                        company_ou_dn = f"OU={company_name},{parent_ou_dn}"
                        stats["created_ous"] += 1
                        # 记录创建的OU详细信息
                        created_ous.append({
                            "name": company_name,
                            "dn": company_ou_dn,
                            "type": "公司",
                            "id": company_id
                        })

                created_ous[company_key] = company_ou_dn

        # 按层级创建部门OU
        # 对部门按照层级深度排序，以确保先创建上级部门
        sorted_depts = sorted(department_hierarchies.items(),
                             key=lambda x: len(x[1]['path'].split(',')))

        # 创建部门OU，先创建层级浅的部门
        for dept_id, dept_info in sorted_depts:
            dept_name = departments[dept_id]
            dept_path = dept_info['path'].split(',')
            # 层级信息可用于日志记录或调试
            _ = dept_info['hierarchy'].split(' > ')

            # 创建部门所需的父级DN
            parent_dn = ""

            # 如果只有一级，则直接挂在公司下
            if len(dept_path) == 1:
                # 查找该部门所属的公司ID
                company_id = None

                # 遍历所有用户查找该部门关联的公司ID
                for user in all_users:
                    if user.DeptID == dept_id and user.CompanyID:
                        company_id = user.CompanyID
                        break

                if company_id and company_id in companies:
                    company_key = f"company_{company_id}"
                    if company_key in created_ous:
                        parent_dn = created_ous[company_key]
                    else:
                        # 如果找不到公司OU，使用根OU
                        logger.warning(f"找不到部门 {dept_name} (ID: {dept_id}) 所属的公司OU，将使用根OU")
                        parent_dn = parent_ou_dn
                else:
                    # 如果找不到公司信息，使用根OU
                    logger.warning(f"找不到部门 {dept_name} (ID: {dept_id}) 所属的公司信息，将使用根OU")
                    parent_dn = parent_ou_dn
            else:
                # 对于多级部门，使用上一级部门作为父级
                parent_dept_id = int(dept_path[-2])  # 倒数第二个是父级部门ID
                parent_key = f"dept_{parent_dept_id}"

                if parent_key in created_ous:
                    parent_dn = created_ous[parent_key]
                else:
                    # 如果找不到父级部门OU，检查是否是因为父级部门没有被处理
                    if parent_dept_id in departments:
                        logger.warning(f"父级部门 {parent_dept_id} 尚未创建OU，当前处理的部门为 {dept_id}")

                    # 在这种情况下，尝试使用更上一级或公司OU
                    company_id = None
                    for user in all_users:
                        if user.DeptID == dept_id and user.CompanyID:
                            company_id = user.CompanyID
                            break

                    if company_id and company_id in companies:
                        company_key = f"company_{company_id}"
                        if company_key in created_ous:
                            parent_dn = created_ous[company_key]
                            logger.warning(f"找不到部门 {dept_name} (ID: {dept_id}) 的父级部门OU，将使用公司OU")
                        else:
                            parent_dn = parent_ou_dn
                            logger.warning(f"找不到部门 {dept_name} (ID: {dept_id}) 的父级部门OU和公司OU，将使用根OU")
                    else:
                        parent_dn = parent_ou_dn
                        logger.warning(f"找不到部门 {dept_name} (ID: {dept_id}) 的父级部门OU和公司信息，将使用根OU")

            # 如果找到了父级DN，创建或更新部门OU
            if parent_dn:
                # 创建或更新部门OU
                dept_key = f"dept_{dept_id}"
                if dept_key not in created_ous:
                    # 首先通过部门ID查找OU
                    dept_search_by_id = await ad_client.search(
                        search_base=parent_dn,
                        search_filter=f"(&(objectClass=organizationalUnit)(|(description=*部门ID:{dept_id}*)(description=*部门ID：{dept_id}*)))",
                        search_scope="one"
                    )

                    if dept_search_by_id:
                        # 部门OU已存在，检查名称是否需要更新
                        dept_ou_dn = dept_search_by_id[0]['dn']
                        existing_ou_name = dept_search_by_id[0]['attributes']['ou'][0]

                        if existing_ou_name != dept_name:
                            # 部门名称已变更，需要重命名OU
                            try:
                                # 重命名OU
                                await ad_client.rename_ou(
                                    dn=dept_ou_dn,
                                    new_name=dept_name
                                )
                                # 更新描述
                                await ad_client.modify_ou(
                                    dn=f"OU={dept_name},{parent_dn}",
                                    attributes={
                                        'description': [f"部门: {dept_name} (部门ID:{dept_id})"]
                                    }
                                )
                                dept_ou_dn = f"OU={dept_name},{parent_dn}"
                                stats["renamed_ous"] = stats.get("renamed_ous", 0) + 1
                                logger.info(f"部门OU重命名成功: {existing_ou_name} -> {dept_name}")
                                # 记录重命名的OU详细信息
                                renamed_ous.append({
                                    "old_name": existing_ou_name,
                                    "new_name": dept_name,
                                    "dn": dept_ou_dn,
                                    "type": "部门",
                                    "id": dept_id
                                })
                            except Exception as e:
                                logger.error(f"重命名部门OU失败 {existing_ou_name} -> {dept_name}: {str(e)}")
                                stats["errors"].append(f"重命名部门OU失败: {str(e)}")
                                # 使用原始DN
                                dept_ou_dn = dept_search_by_id[0]['dn']
                        else:
                            # 部门名称未变更，使用已存在的OU
                            stats["skipped_ous"] = stats.get("skipped_ous", 0) + 1
                    else:
                        # 尝试通过名称查找
                        escaped_dept_name = ad_client.escape_dn_for_filter(dept_name)
                        dept_search_by_name = await ad_client.search(
                            search_base=parent_dn,
                            search_filter=f"(&(objectClass=organizationalUnit)(ou={escaped_dept_name}))",
                            search_scope="one"
                        )

                        if dept_search_by_name:
                            # 如果找到同名OU，更新其描述以包含部门ID
                            dept_ou_dn = dept_search_by_name[0]['dn']
                            await ad_client.modify_ou(
                                dn=dept_ou_dn,
                                attributes={
                                    'description': [f"部门: {dept_name} (部门ID:{dept_id})"]
                                }
                            )
                            stats["skipped_ous"] = stats.get("skipped_ous", 0) + 1
                        else:
                            # 创建新部门OU，描述中包含部门ID
                            await ad_client.create_ou(
                                parent_dn=parent_dn,
                                name=dept_name,
                                description=f"部门: {dept_name} (部门ID:{dept_id})"
                            )
                            dept_ou_dn = f"OU={dept_name},{parent_dn}"
                            stats["created_ous"] += 1
                            # 记录创建的OU详细信息
                            created_ous.append({
                                "name": dept_name,
                                "dn": dept_ou_dn,
                                "type": "部门",
                                "id": dept_id
                            })

                    created_ous[dept_key] = dept_ou_dn
            else:
                logger.warning(f"无法确定部门 {dept_name} (ID: {dept_id}) 的父级OU，跳过创建")

        # 恢复默认超时时间
        await ad_client.set_connection_timeout(15)  # 恢复15秒超时

        # 检查并移动现有安全组到对应的OU
        moved_groups = 0
        groups_errors = []

        if created_ous:  # 只有在有新创建或更新的OU时才执行
            try:
                # 获取所有安全组
                all_groups = await ad_client.get_all_groups()
                logger.info(f"获取到 {len(all_groups)} 个安全组，准备检查移动")

                # 处理前先检查AD连接状态
                if not await ad_client.ensure_connected():
                    logger.error("AD连接不可用，无法处理安全组移动")
                    groups_errors.append("AD连接不可用，无法处理安全组移动")
                else:
                    # 提前验证所有目标OU是否存在
                    invalid_ous = []
                    for ou_dn in set(created_ous.values()):
                        if not await ad_client.check_ou_exists(ou_dn):
                            invalid_ous.append(ou_dn)
                            logger.error(f"目标OU不存在: {ou_dn}")

                    if invalid_ous:
                        groups_errors.append(f"以下目标OU不存在: {', '.join(invalid_ous)}")

                    for group in all_groups:
                        # 从安全组描述中提取部门ID
                        # 确保description是字符串类型
                        description = group.get('description')
                        if description is None:
                            description = ''

                        dept_id_match = re.search(r'部门ID[：:]\s*(\d+)', description)
                        if dept_id_match:
                            dept_id = int(dept_id_match.group(1))
                            dept_key = f"dept_{dept_id}"

                            # 检查该部门是否在已创建的OU列表中
                            if dept_key in created_ous:
                                # 当前安全组所在的OU
                                current_group_dn = group.get('dn', '')
                                target_ou_dn = created_ous[dept_key]

                                # 判断安全组是否需要移动
                                try:
                                    # 从DN中提取当前OU
                                    dn_parts = current_group_dn.split(',')
                                    if len(dn_parts) > 1:
                                        current_ou_parts = ','.join(dn_parts[1:])  # 去掉CN=GroupName部分

                                        # 如果安全组不在对应的OU下，需要移动
                                        if current_ou_parts != target_ou_dn:
                                            # 提取安全组名称
                                            group_name = group.get('name', '')
                                            if not group_name:
                                                logger.warning(f"安全组名称为空，DN: {current_group_dn}")
                                                continue

                                            # 内置组不应该移动
                                            if group.get('isBuiltin', False):
                                                logger.info(f"跳过内置安全组 '{group_name}' 的移动")
                                                continue

                                            try:
                                                # 移动安全组到正确的OU
                                                logger.info(f"尝试移动安全组 '{group_name}' 从 {current_ou_parts} 到 {target_ou_dn}")

                                                # 使用专用函数移动安全组
                                                move_result = await move_security_group(current_group_dn, target_ou_dn, group_name)

                                                if move_result["success"]:
                                                    moved_groups += 1
                                                    logger.info(f"已移动安全组 '{group_name}' 到 {target_ou_dn}")
                                                else:
                                                    error_msg = move_result.get("error", f"移动安全组 '{group_name}' 到 {target_ou_dn} 失败")
                                                    groups_errors.append(error_msg)
                                                    logger.error(error_msg)
                                            except Exception as e:
                                                error_msg = f"移动安全组 '{group_name}' 时出错: {str(e)}"
                                                groups_errors.append(error_msg)
                                                logger.error(error_msg)
                                    else:
                                        logger.warning(f"安全组DN格式异常: {current_group_dn}")
                                except Exception as e:
                                    logger.error(f"处理安全组DN时出错: {str(e)}")
            except Exception as e:
                logger.error(f"检查并移动安全组过程中出错: {str(e)}")
                tb = traceback.format_exc()
                logger.error(f"详细错误信息: {tb}")
                groups_errors.append(f"检查安全组错误: {str(e)}")

        # 将安全组移动结果添加到统计信息中
        stats["moved_groups"] = moved_groups
        if groups_errors:
            stats["errors"].extend(groups_errors)

        # 更新返回消息，明确说明只同步了包含在职人员的部门
        return {
            "message": f"组织结构同步完成。处理了 {stats['total_departments']} 个部门(仅包含在职人员的部门)，创建了 {stats['created_ous']} 个新OU，重命名了 {stats.get('renamed_ous', 0)} 个OU，跳过了 {stats.get('skipped_ous', 0)} 个已存在的OU，移动了 {moved_groups} 个安全组",
            "stats": stats,
            "created_ous": created_ous,  # 返回创建的OU信息，方便后续用户同步使用
            "created_ous": created_ous,  # 添加创建的OU详细信息
            "renamed_ous": renamed_ous  # 添加重命名的OU详细信息
        }
    except Exception as e:
        logger.error(f"同步组织结构失败: {str(e)}")
        return {
            "message": f"同步组织结构失败: {str(e)}",
            "stats": {
                "total_companies": 0,
                "total_departments": 0,
                "created_ous": 0,
                "errors": [str(e)]
            },
            "created_ous": {},
            "created_ous": [],
            "renamed_ous": []
        }

async def sync_from_personnel(sync_data: schemas.ADSyncFromPersonnel) -> Dict:
    """从基础信息-人员信息同步人员信息到AD域"""
    try:
        # 记录开始时间
        start_time = time.time()
        
        # 导入本地人员信息API
        from app.crud import ecology_user as ecology_user_crud
        from app.api.deps import get_db
        from app.database import db_context
        
        # 在同步操作开始前清除所有AD相关缓存，确保使用最新数据
        await ad_client._clear_cache()
        logger.info("已清除AD客户端缓存，确保使用最新数据进行同步")
        
        # 验证目标OU
        if not await validate_ou_dn(sync_data.parent_ou_dn):
            return {
                "success": False,
                "message": f"目标OU '{sync_data.parent_ou_dn}' 无效或不存在",
                "stats": {
                    "errors": [f"目标OU '{sync_data.parent_ou_dn}' 无效"]
                }
            }

        # 设置更长的连接超时时间
        await ad_client.set_connection_timeout(30)  # 30秒超时

        # 首先同步组织结构（如果用户开启了创建组织结构选项）
        created_ous = {}
        org_created_ous_count = 0
        created_ous_data = []
        renamed_ous_data = []
        if sync_data.create_ou:
            try:
                org_result = await sync_organization_structure(
                    parent_ou_dn=sync_data.parent_ou_dn,
                    company_id=sync_data.company_id,
                    dept_id=sync_data.dept_id
                )
                created_ous = org_result.get("created_ous", {})
                org_created_ous_count = org_result.get("stats", {}).get("created_ous", 0)
                created_ous_data = org_result.get("created_ous", [])
                renamed_ous_data = org_result.get("renamed_ous", [])
                logger.info(f"组织结构同步完成: {org_result['message']}")
            except Exception as e:
                logger.error(f"组织结构同步失败: {str(e)}")
                # 记录错误但继续执行，以便用户依然可以同步

        # 获取组织部门数据用于创建安全组
        all_departments = []
        if sync_data.create_security_groups:
            # 从泛微导入需要的API函数
            from app.api.v1.ecology import (
                get_ecology_departments,
                get_departments_by_company,
                get_departments_by_parent_recursive,
                get_department_by_id
            )
            
            # 根据筛选条件获取部门
            if sync_data.company_id:
                # 按公司获取部门
                all_departments = get_departments_by_company(sync_data.company_id)
            elif sync_data.dept_id:
                # 按部门获取子部门（使用递归获取所有层级的子部门）
                all_departments = get_departments_by_parent_recursive(sync_data.dept_id)
                # 添加当前部门自身
                current_dept = get_department_by_id(sync_data.dept_id)
                if current_dept:
                    all_departments.append(current_dept)
            else:
                # 获取所有部门
                all_departments = get_ecology_departments()

            logger.info(f"获取到 {len(all_departments)} 个部门信息, 准备创建安全组")

        # 使用数据库会话
        with db_context() as db:
            # 从本地数据库获取用户数据
            # 构建筛选条件
            keyword = None
            exact_match = False
            limit = 100000  # 大数值以获取所有用户
            
            # 根据筛选条件获取所有用户（包括非在职人员）
            all_users = ecology_user_crud.get_ecology_users(db, skip=0, limit=limit, keyword=keyword, exact_match=exact_match)
            
            # 转换为同样的数据结构
            converted_users = []
            for user in all_users:
                # 创建兼容的数据结构
                converted_user = EcologySchema(
                    DeptID=user.dept_id,
                    DeptName=user.dept_name,
                    DeptHierarchy=user.dept_hierarchy,
                    Level=user.level,
                    DeptPath=user.dept_path,
                    CompanyID=user.company_id,
                    CompanyName=user.company_name,
                    UserID=user.user_id,
                    UserName=user.user_name,
                    JobNumber=user.job_number,
                    Mobile=user.mobile,
                    Email=user.email,
                    JobTitle=user.job_title,
                    JobTitleName=user.job_title_name,
                    Gender=user.gender,
                    Status=user.status
                )
                converted_users.append(converted_user)

            # 过滤特定公司或部门的用户
            if sync_data.company_id:
                all_users = [user for user in converted_users if user.CompanyID == sync_data.company_id]
                logger.info(f"按公司ID {sync_data.company_id} 过滤后，获取到 {len(all_users)} 个用户")
            elif sync_data.dept_id:
                # 注意：这里只包含直接属于该部门的用户，不包括子部门
                # 如需包括子部门用户，需要额外处理
                all_users = [user for user in converted_users if user.DeptID == sync_data.dept_id]
                logger.info(f"按部门ID {sync_data.dept_id} 过滤后，获取到 {len(all_users)} 个用户")
                
                # 如果需要包含子部门用户，需要额外处理
                if sync_data.dept_id:
                    # 导入所需功能
                    from app.api.v1.ecology import get_departments_by_parent_recursive
                    
                    # 获取所有子部门ID
                    subdepts = get_departments_by_parent_recursive(sync_data.dept_id)
                    subdept_ids = [dept["ID"] for dept in subdepts]
                    
                    # 添加子部门的用户
                    child_users = [user for user in converted_users if user.DeptID in subdept_ids]
                    all_users.extend(child_users)
                    logger.info(f"加上子部门用户后，共获取到 {len(all_users)} 个用户")
            else:
                all_users = converted_users

        # 在职状态的人员列表
        in_service_statuses = ['试用', '正式', '临时', '试用延期']
        # 非在职状态的人员列表（离职、解聘等）
        non_active_statuses = ['离职', '解聘', '退休', '无效']

        # 分离在职和非在职人员
        in_service_users = [user for user in all_users if user.Status in in_service_statuses]
        non_active_users = [user for user in all_users if user.Status in non_active_statuses]

        # 记录处理统计信息
        stats = {
            "total": len(in_service_users),
            "created_users": 0,
            "skipped_users": 0,
            "updated_users": 0,  # 新增：更新的用户数量
            "moved_users": 0,    # 新增：移动的用户数量
            "disabled_users": 0,  # 新增：禁用的用户数量
            "whitelist_skipped_users": 0,  # 新增：白名单跳过的用户数量
            "created_ous": org_created_ous_count,  # 使用组织结构同步创建的OU数量
            "renamed_ous": len(renamed_ous_data) if renamed_ous_data else 0,
            "created_groups": 0,  # 新增：创建的安全组数量
            "added_to_groups": 0,  # 新增：添加到安全组的用户数量
            "removed_from_groups": 0,  # 新增：从安全组移除的用户数量
            "moved_groups": 0,    # 新增：移动的安全组数量
            "errors": [],
            "new_users": [],  # 添加新用户列表，用于记录用户名和密码
            "updated_users_details": [],  # 添加更新用户的详细信息列表
            "disabled_users_details": [],  # 添加禁用用户列表，用于记录被禁用的用户信息
            "whitelist_skipped_users_details": [],  # 添加白名单跳过用户列表
            "created_ous_details": created_ous_data,  # 创建的OU详细信息
            "renamed_ous_details": renamed_ous_data,  # 重命名的OU详细信息
            "created_groups_details": [],  # 添加创建的安全组详细信息
            "updated_groups_details": [],  # 添加更新的安全组详细信息
            "added_to_groups_details": [],  # 添加用户到安全组的详细信息
            "removed_from_groups_details": []  # 添加从安全组移除用户的详细信息
        }

        # 如果需要创建安全组，先创建部门对应的安全组
        if sync_data.create_security_groups and all_departments:
            logger.info("开始创建部门安全组...")

            # 获取现有的安全组列表，用于检查是否已存在
            existing_groups = await ad_client.get_all_groups()  # 不传参数，获取所有安全组
            existing_group_names = [g['name'].lower() for g in existing_groups]

            # 构建部门ID到安全组名称的映射，用于批量处理
            dept_group_names = {}

            # 收集所有部门名称，检查是否有重名
            dept_names_count = {}
            for dept in all_departments:
                if dept.get('DeptName'):
                    dept_names_count[dept['DeptName']] = dept_names_count.get(dept['DeptName'], 0) + 1

            # 记录重名的部门
            duplicate_dept_names = {name for name, count in dept_names_count.items() if count > 1}
            if duplicate_dept_names:
                logger.info(f"检测到重名部门: {', '.join(duplicate_dept_names)}")

            # 获取部门层级关系，用于重名部门处理
            dept_hierarchy = {}
            for dept in all_departments:
                if dept.get('ID') and dept.get('SuperiorID'):
                    dept_hierarchy[dept['ID']] = dept['SuperiorID']

            for dept in all_departments:
                # 跳过没有ID或名称的部门
                if not dept.get('ID') or not dept.get('DeptName'):
                    continue

                # 使用新的函数生成友好的安全组名称
                group_name = build_group_name_for_dept(dept, all_departments, duplicate_dept_names)
                dept_group_names[dept['ID']] = group_name

            # 找出需要创建的安全组
            groups_to_create = []
            for dept_id, group_name in dept_group_names.items():
                if group_name.lower() not in existing_group_names:
                    # 找对应的部门数据
                    dept_data = next((d for d in all_departments if d.get('ID') == dept_id), None)
                    if dept_data:
                        groups_to_create.append({
                            'dept_id': dept_id,
                            'name': group_name,
                            'dept_name': dept_data.get('DeptName', ''),
                            'company_id': dept_data.get('CompanyID')
                        })
                else:
                    logger.info(f"安全组 '{group_name}' 已存在，跳过创建")

                    # 检查现有安全组是否包含正确的部门ID信息，如果没有则更新
                    existing_group = next((g for g in existing_groups if g['name'].lower() == group_name.lower()), None)
                    if existing_group:
                        extracted_id = extract_dept_id_from_group(existing_group.get('description', ''))
                        if extracted_id != dept_id:
                            # 更新安全组描述，确保包含正确的部门ID
                            try:
                                dept_data = next((d for d in all_departments if d.get('ID') == dept_id), None)
                                if dept_data:
                                    # 在描述中加入部门ID信息
                                    desc = f"部门 {dept_data['DeptName']} 的安全组 (部门ID: {dept_id})"
                                    await ad_client.update_group(existing_group['dn'], {'description': desc})
                                    logger.info(f"更新安全组 '{group_name}' 的描述，添加部门ID: {dept_id}")
                                    # 记录更新的安全组详细信息
                                    stats["updated_groups_details"].append({
                                        "name": group_name,
                                        "dn": existing_group['dn'],
                                        "changed_fields": "description"
                                    })
                            except Exception as e:
                                logger.warning(f"更新安全组 '{group_name}' 描述时出错: {str(e)}")

            # 创建安全组
            created_count = 0
            for group_info in groups_to_create:
                try:
                    # 确定安全组应该放在哪个OU下
                    target_ou_dn = sync_data.parent_ou_dn

                    if sync_data.create_ou:
                        # 尝试找到对应部门的OU
                        dept_key = f"dept_{group_info['dept_id']}"
                        if dept_key in created_ous:
                            target_ou_dn = created_ous[dept_key]
                            logger.info(f"找到部门 {group_info['dept_name']} (ID: {group_info['dept_id']}) 对应的OU: {target_ou_dn}")
                        else:
                            # 如果找不到对应的部门OU，尝试获取OU树，然后查找对应的DN
                            logger.info(f"在created_ous中未找到部门 {group_info['dept_name']} (ID: {group_info['dept_id']}) 的OU，尝试查找其他方式")

                            # 打印创建的OU信息以便调试
                            logger.debug(f"已创建的OU列表: {created_ous}")

                            # 尝试在OU树中查找部门
                            ou_tree = await get_ou_tree()
                            if ou_tree:
                                # 首先尝试使用部门名称直接匹配
                                ou_dn = await find_ou_dn(group_info['dept_name'], ou_tree)
                                if ou_dn:
                                    logger.info(f"通过名称匹配找到部门OU: {ou_dn}")
                                    target_ou_dn = ou_dn
                                else:
                                    # 如果通过名称没找到，尝试在父级OU中查找
                                    # 如果有公司信息，先尝试找到公司OU
                                    if group_info.get('company_id'):
                                        company_key = f"company_{group_info['company_id']}"
                                        if company_key in created_ous:
                                            company_ou_dn = created_ous[company_key]
                                            logger.info(f"找到公司OU: {company_ou_dn}，使用公司OU")
                                            target_ou_dn = company_ou_dn

                    try:
                        # 如果找不到特定OU，尝试使用改进的方法查找
                        if sync_data.create_ou and dept_key not in created_ous:
                            # 获取OU树
                            ou_tree = await get_ou_tree()
                            if ou_tree:
                                # 使用改进的方法，通过部门ID和名称查找OU
                                specific_ou_dn = await find_ou_by_dept_id(
                                    dept_id=group_info['dept_id'],
                                    dept_name=group_info['dept_name'],
                                    ou_tree=ou_tree,
                                    created_ous=created_ous
                                )
                                if specific_ou_dn:
                                    logger.info(f"使用精确匹配找到部门 {group_info['dept_name']} 的OU: {specific_ou_dn}")
                                    target_ou_dn = specific_ou_dn
                    except Exception as e:
                        logger.warning(f"查找部门 {group_info['dept_name']} 的OU时出错: {str(e)}")
                        # 继续使用默认的target_ou_dn

                    # 创建安全组
                    # 在描述中添加部门ID信息，确保后续能通过部门ID找到对应安全组
                    group_desc = f"部门 {group_info['dept_name']} 的安全组 (部门ID: {group_info['dept_id']})"
                    logger.info(f"尝试在 {target_ou_dn} 创建安全组 {group_info['name']}")

                    # 在创建安全组之前，检查是否存在同名安全组
                    existing_group = await ad_client.get_group_by_name(group_info['name'])
                    if existing_group:
                        logger.info(f"安全组 {group_info['name']} 已存在，使用现有安全组")
                        # 检查现有安全组描述中是否包含正确的部门ID
                        extracted_id = extract_dept_id_from_group(existing_group.get('description', ''))
                        if extracted_id != group_info['dept_id']:
                            # 更新安全组描述，确保包含正确的部门ID
                            await ad_client.update_group(existing_group['dn'], {'description': group_desc})
                            logger.info(f"更新安全组 '{group_info['name']}' 的描述，添加部门ID: {group_info['dept_id']}")
                            # 记录更新的安全组详细信息
                            stats["updated_groups_details"].append({
                                "name": group_info['name'],
                                "dn": existing_group['dn'],
                                "changed_fields": "description"
                            })

                        stats["created_groups"] += 0  # 不增加计数
                        created_count += 0
                    else:
                        success = await ad_client.create_group(
                            name=group_info['name'],
                            ou_dn=target_ou_dn,
                            description=group_desc
                        )

                        if success:
                            stats["created_groups"] += 1
                            created_count += 1
                            # 添加到详细信息列表
                            stats["created_groups_details"].append({
                                "name": group_info['name'],
                                "dn": target_ou_dn
                            })
                            logger.info(f"成功创建安全组: {group_info['name']} 在 {target_ou_dn}")
                        else:
                            error_msg = f"创建安全组失败: {group_info['name']}"
                            logger.error(error_msg)
                            stats["errors"].append(error_msg)

                except Exception as e:
                    error_msg = f"处理部门 {group_info.get('dept_name', '未知')} 安全组时出错: {str(e)}"
                    logger.error(error_msg)
                    stats["errors"].append(error_msg)

            logger.info(f"部门安全组创建完成，共创建 {created_count} 个新安全组")

            # 检查并修正被人为修改的安全组名称
            if sync_data.create_security_groups and sync_data.auto_rename_security_groups:
                logger.info("开始检查和修正被人为修改的安全组名称...")
                renamed_count = 0

                # 先收集所有安全组名称（不区分大小写），用于检查重名情况
                all_group_names = {}  # 用于存储 {标准名称小写: [对应的组]}
                for group in existing_groups:
                    if 'name' in group and group['name']:
                        group_name_lower = group['name'].lower()
                        if group_name_lower not in all_group_names:
                            all_group_names[group_name_lower] = []
                        all_group_names[group_name_lower].append(group)

                # 查找所有通过部门ID可识别的安全组
                groups_to_process = []  # 需要处理的组列表 [(组信息, 新名称, 部门ID)]

                for group in existing_groups:
                    if 'description' in group and group['description']:
                        extracted_dept_id = extract_dept_id_from_group(group['description'])
                        if extracted_dept_id is not None:
                            # 找到部门ID对应的标准安全组名称
                            standard_group_name = dept_group_names.get(extracted_dept_id)
                            if standard_group_name and group['name'] != standard_group_name:
                                # 安全组名称与标准名称不一致，需要修正
                                logger.info(f"检测到安全组名称被修改: 当前名称 '{group['name']}', 标准名称 '{standard_group_name}' (部门ID: {extracted_dept_id})")
                                groups_to_process.append((group, standard_group_name, extracted_dept_id))

                # 按部门ID分组处理
                dept_id_to_groups = {}
                for group, std_name, dept_id in groups_to_process:
                    if dept_id not in dept_id_to_groups:
                        dept_id_to_groups[dept_id] = []
                    dept_id_to_groups[dept_id].append((group, std_name))

                # 处理每个部门ID下的安全组
                for dept_id, group_list in dept_id_to_groups.items():
                    if not group_list:
                        continue
                        
                    target_name = group_list[0][1]  # 标准名称
                    target_name_lower = target_name.lower()

                    # 检查是否已有符合标准名称的安全组
                    existing_standard_groups = all_group_names.get(target_name_lower, [])
                    existing_standard_group = None

                    # 找到同部门ID的标准名称安全组
                    for group in existing_standard_groups:
                        group_dept_id = extract_dept_id_from_group(group.get('description', ''))
                        if group_dept_id == dept_id:
                            existing_standard_group = group
                            break

                    if existing_standard_group:
                        # 已存在标准名称的安全组，合并其他同部门的安全组成员
                        logger.info(f"部门ID {dept_id} 已有标准名称安全组 '{target_name}'，将合并其他同部门安全组成员")
                        
                        for group_info, _ in group_list:
                            if group_info['dn'] != existing_standard_group['dn']:
                                try:
                                    # 获取该组的所有成员
                                    members = await ad_client.get_group_member_dns(group_info['dn'])
                                    if members:
                                        # 将成员添加到标准名称的组
                                        for member in members:
                                            await ad_client.add_member_to_group(member, existing_standard_group['dn'])
                                        logger.info(f"已将安全组 '{group_info['name']}' 的 {len(members)} 个成员添加到标准组 '{target_name}'")

                                    # 标记为待删除（保留原逻辑用于清理冗余组）
                                    new_desc = f"[待删除] 已弃用的安全组，成员已迁移到 {target_name}，标记删除时间: {datetime.now().strftime('%Y-%m-%d')}"
                                    await ad_client.update_group(group_info['dn'], {'description': new_desc})
                                    logger.info(f"已更新安全组 '{group_info['name']}' 的描述，标记为待删除")
                                    renamed_count += 1

                                except Exception as e:
                                    error_msg = f"合并安全组 '{group_info['name']}' 成员到 '{target_name}' 时出错: {str(e)}"
                                    logger.error(error_msg)
                                    stats["errors"].append(error_msg)
                    else:
                        # 不存在标准名称的安全组，直接重命名第一个安全组为标准名称
                        if group_list:
                            primary_group, std_name = group_list[0]
                            
                            # 检查目标名称是否被其他安全组占用（不同部门ID）
                            name_conflict = False
                            for existing_group in existing_standard_groups:
                                conflict_dept_id = extract_dept_id_from_group(existing_group.get('description', ''))
                                if conflict_dept_id is not None and conflict_dept_id != dept_id:
                                    name_conflict = True
                                    logger.warning(f"目标名称 '{std_name}' 已被部门ID {conflict_dept_id} 的安全组占用")
                                    break
                            
                            if not name_conflict:
                                try:
                                    # 直接重命名主安全组为标准名称
                                    # 获取部门名称用于描述
                                    dept_info = next((d for d in all_departments if d.get('ID') == dept_id), None)
                                    dept_name = dept_info.get('DeptName', '未知') if dept_info else '未知'
                                    
                                    await ad_client.update_group(primary_group['dn'], {
                                        'name': std_name,
                                        'description': f"部门 {dept_name} 的安全组 (部门ID: {dept_id})"
                                    })
                                    logger.info(f"已直接重命名安全组: '{primary_group['name']}' -> '{std_name}' (保持权限连续性)")
                                    renamed_count += 1
                                    
                                    # 更新内存中的组名称，避免后续处理冲突
                                    primary_group['name'] = std_name
                                    # 更新all_group_names映射
                                    if std_name.lower() not in all_group_names:
                                        all_group_names[std_name.lower()] = []
                                    all_group_names[std_name.lower()].append(primary_group)

                                except Exception as e:
                                    error_msg = f"重命名安全组 '{primary_group['name']}' 到 '{std_name}' 时出错: {str(e)}"
                                    logger.error(error_msg)
                                    stats["errors"].append(error_msg)
                                    continue

                            # 处理同部门的其他安全组
                            for group_info, _ in group_list[1:]:  # 跳过第一个（已重命名的主组）
                                try:
                                    # 获取该组的所有成员
                                    members = await ad_client.get_group_member_dns(group_info['dn'])
                                    if members:
                                        # 将成员添加到重命名后的主组
                                        for member in members:
                                            await ad_client.add_member_to_group(member, primary_group['dn'])
                                        logger.info(f"已将安全组 '{group_info['name']}' 的 {len(members)} 个成员添加到重命名后的主组 '{std_name}'")

                                    # 标记为待删除
                                    new_desc = f"[待删除] 已弃用的安全组，成员已迁移到 {std_name}，标记删除时间: {datetime.now().strftime('%Y-%m-%d')}"
                                    await ad_client.update_group(group_info['dn'], {'description': new_desc})
                                    logger.info(f"已更新安全组 '{group_info['name']}' 的描述，标记为待删除")
                                    renamed_count += 1

                                except Exception as e:
                                    error_msg = f"处理安全组 '{group_info['name']}' 时出错: {str(e)}"
                                    logger.error(error_msg)
                                    stats["errors"].append(error_msg)

                logger.info(f"安全组名称检查和重命名完成，共处理 {renamed_count} 个安全组（采用直接重命名策略保持权限连续性）")
                stats["renamed_groups"] = renamed_count

                # 由于可能进行了安全组重命名，需要重新获取安全组列表
                if renamed_count > 0:
                    all_groups = await ad_client.get_all_groups()

            # 为后续用户添加到安全组做准备，创建部门ID到安全组DN的映射
            if sync_data.create_security_groups:
                # 重新获取所有安全组，包括新创建的和重命名的
                all_groups = await ad_client.get_all_groups()

                # 创建部门ID到安全组DN的映射
                dept_to_group_dn = {}
                for dept_id, group_name in dept_group_names.items():
                    # 在所有安全组中查找匹配的组
                    group = next((g for g in all_groups if g['name'].lower() == group_name.lower()), None)
                    if group and 'dn' in group:
                        dept_to_group_dn[dept_id] = group['dn']
                        logger.info(f"找到部门ID {dept_id} 对应的安全组DN: {group['dn']}")

                logger.info(f"成功映射 {len(dept_to_group_dn)} 个部门ID到安全组DN")

        # 处理在职人员 - 创建新用户或更新现有用户
        # 收集所有在职人员工号，批量查询状态
        in_service_job_numbers = [user.JobNumber for user in in_service_users if user.UserID and user.JobNumber]

        # 统计变量初始化
        to_enable = []
        create_count = 0
        update_count = 0
        move_count = 0

        if in_service_job_numbers:
            logger.info(f"开始处理 {len(in_service_job_numbers)} 个在职人员账号...")

            # 用于存储用户数据，方便后续处理
            user_map = {user.JobNumber: user for user in in_service_users if user.UserID and user.JobNumber}

            # 创建新用户前，先确认哪些用户已存在
            existing_users = {}
            # 每次处理100个用户
            batch_size = 100
            total_queried = 0
            total_found = 0

            # 批量查询所有用户状态
            for i in range(0, len(in_service_job_numbers), batch_size):
                batch_numbers = in_service_job_numbers[i:i+batch_size]
                current_batch = len(batch_numbers)
                total_queried += current_batch

                logger.info(f"批量查询第 {i//batch_size + 1} 批在职用户，共 {current_batch} 个 ({total_queried}/{len(in_service_job_numbers)})")
                batch_users = await ad_client.get_users_batch(batch_numbers, no_cache=True)

                # 统计在AD中找到的用户数
                found_users = [username for username, info in batch_users.items() if info is not None]
                batch_found = len(found_users)
                total_found += batch_found

                logger.info(f"批次查询结果: 在AD中找到 {batch_found}/{current_batch} 个用户")
                existing_users.update(batch_users)

            # 统计需要创建和更新的用户数
            to_create = [job_number for job_number in in_service_job_numbers if existing_users.get(job_number) is None]
            to_update = [job_number for job_number in in_service_job_numbers if existing_users.get(job_number) is not None]

            # 需要启用的用户列表
            to_enable = [job_number for job_number, info in existing_users.items()
                        if info and not info.get('enabled', True)]

            logger.info(f"用户状态统计: 总计 {len(in_service_job_numbers)} 个在职用户, " +
                      f"需要创建 {len(to_create)} 个新用户, " +
                      f"需要更新 {len(to_update)} 个现有用户, " +
                      f"需要启用 {len(to_enable)} 个被禁用用户")

            # 按批次处理用户
            create_count = 0
            update_count = 0
            move_count = 0

            for job_number, hrm_user in user_map.items():
                try:
                    # 确定用户应该放在哪个OU下
                    target_ou_dn = sync_data.parent_ou_dn

                    if sync_data.create_ou:
                        # 尝试找到最深层级的部门OU
                        if hrm_user.DeptID:
                            dept_key = f"dept_{hrm_user.DeptID}"
                            if dept_key in created_ous:
                                target_ou_dn = created_ous[dept_key]
                            else:
                                # 如果找不到对应的部门OU，尝试找公司OU
                                if hrm_user.CompanyID:
                                    company_key = f"company_{hrm_user.CompanyID}"
                                    if company_key in created_ous:
                                        target_ou_dn = created_ous[company_key]

                    # 获取AD中已存在的用户信息
                    existing_user = existing_users.get(job_number)

                    if existing_user:
                        # 用户已存在

                        # 如果用户存在但被禁用，需要重新启用
                        if not existing_user.get('enabled', True):
                            # 添加到待启用列表
                            to_enable.append(job_number)

                        # 更新用户基本信息
                        update_attrs = {}
                        if existing_user.get('name') != hrm_user.UserName:
                            update_attrs['name'] = hrm_user.UserName
                        if existing_user.get('email') != hrm_user.Email and hrm_user.Email:
                            update_attrs['email'] = hrm_user.Email
                        if existing_user.get('department') != hrm_user.DeptName and hrm_user.DeptName:
                            update_attrs['department'] = hrm_user.DeptName
                        if existing_user.get('title') != hrm_user.JobTitleName and hrm_user.JobTitleName:
                            update_attrs['title'] = hrm_user.JobTitleName

                        # 如果有属性需要更新
                        if update_attrs:
                            # 记录更新前后的值
                            field_changes = []
                            for field, new_value in update_attrs.items():
                                old_value = ""
                                # 根据AD属性映射获取旧值
                                if field == 'name':
                                    old_value = existing_user.get('name', '')
                                elif field == 'email':
                                    old_value = existing_user.get('email', '')
                                elif field == 'department':
                                    old_value = existing_user.get('department', '')
                                elif field == 'title':
                                    old_value = existing_user.get('title', '')

                                field_changes.append({
                                    "field": field,
                                    "old_value": old_value,
                                    "new_value": new_value
                                })

                            await ad_client.update_user(job_number, update_attrs)
                            stats["updated_users"] += 1
                            update_count += 1  # 更新本地统计计数
                            logger.info(f"已更新用户信息: {job_number} ({hrm_user.UserName})")

                            # 记录更新用户的详细信息
                            stats["updated_users_details"] = stats.get("updated_users_details", [])
                            stats["updated_users_details"].append({
                                "username": job_number,
                                "name": hrm_user.UserName,
                                "department": hrm_user.DeptName if hrm_user.DeptName else "",
                                "changed_fields": ", ".join(update_attrs.keys()),  # 记录哪些字段被更新
                                "dn": existing_user.get('dn', ''),
                                "changes": field_changes  # 记录每个字段的变更详情
                            })

                        # 如果开启了安全组创建，将用户添加到对应部门的安全组
                        if sync_data.create_security_groups and sync_data.add_users_to_dept_groups and hrm_user.DeptID:
                            if hrm_user.DeptID in dept_to_group_dn:
                                group_dn = dept_to_group_dn[hrm_user.DeptID]
                                try:
                                    # 将用户添加到部门安全组，并获取是否有实际添加操作
                                    group_success, actually_added = await ad_client.add_user_to_groups(job_number, [group_dn])
                                    if group_success and actually_added:
                                        stats["added_to_groups"] += 1
                                        logger.info(f"已将用户 {job_number} ({hrm_user.UserName}) 添加到部门安全组")
                                        # 记录添加到安全组的详细信息
                                        group_info = await ad_client.get_group_by_dn(group_dn)
                                        group_name = group_info.get('name', '') if group_info else '未知安全组'
                                        stats["added_to_groups_details"].append({
                                            "username": job_number,
                                            "name": hrm_user.UserName,
                                            "group_name": group_name,
                                            "group_dn": group_dn
                                        })
                                    elif not group_success:
                                        logger.warning(f"将用户 {job_number} 添加到部门安全组 {group_dn} 失败")
                                except Exception as e:
                                    error = f"将用户 {job_number} 添加到安全组时出错: {str(e)}"
                                    logger.error(error)
                                    stats["errors"].append(error)

                        # 检查用户是否需要移动到新的部门OU
                        if sync_data.create_ou and sync_data.move_users_with_dept:
                            # 提取当前用户所在的OU
                            current_dn = existing_user.get('dn', '')

                            # 如果当前DN与目标OU不匹配，说明用户需要移动
                            if current_dn and target_ou_dn not in current_dn:
                                try:
                                    # 移动用户到正确的部门OU
                                    move_result = await move_user(job_number, target_ou_dn)
                                    if move_result.get("message") == "用户移动成功":
                                        stats["moved_users"] += 1
                                        move_count += 1  # 更新本地统计计数
                                        logger.info(f"已移动用户 {job_number} ({hrm_user.UserName}) 到 {target_ou_dn}")
                                except Exception as e:
                                    error = f"移动用户 {job_number} ({hrm_user.UserName}) 时出错: {str(e)}"
                                    logger.error(error)
                                    stats["errors"].append(error)

                        # 处理部门变更的安全组调整
                        if sync_data.create_security_groups and sync_data.update_user_groups_with_dept and hrm_user.DeptID:
                            try:
                                # 获取用户当前的安全组
                                current_groups = await ad_client.get_user_groups(job_number)

                                # 查找用户当前所在的部门安全组（如果有的话）
                                current_dept_groups = []
                                for group_dn in current_groups:
                                    # 不再通过DEPT_前缀识别，改为通过获取安全组详情，检查描述中是否包含部门ID
                                    try:
                                        group_info = await ad_client.get_group_by_dn(group_dn)
                                        if group_info and group_info.get('description'):
                                            extracted_dept_id = extract_dept_id_from_group(group_info['description'])
                                            if extracted_dept_id is not None:
                                                current_dept_groups.append(group_dn)
                                    except Exception as e:
                                        logger.warning(f"获取用户组信息失败: {str(e)}")

                                # 如果用户当前在部门安全组中，且部门ID对应的安全组存在
                                if current_dept_groups and hrm_user.DeptID in dept_to_group_dn:
                                    new_dept_group_dn = dept_to_group_dn[hrm_user.DeptID]

                                    # 如果用户不在新部门安全组中，需要添加
                                    if new_dept_group_dn not in current_groups:
                                        # 添加到新部门安全组
                                        add_success, actually_added = await ad_client.add_user_to_groups(job_number, [new_dept_group_dn])
                                        if add_success and actually_added:
                                            logger.info(f"已将用户 {job_number} ({hrm_user.UserName}) 添加到新部门安全组 {new_dept_group_dn}")
                                            stats["added_to_groups"] += 1
                                        elif not add_success:
                                            logger.warning(f"将用户 {job_number} 添加到新部门安全组 {new_dept_group_dn} 失败")

                                    # 从原部门安全组中移除
                                    groups_to_remove = []
                                    for old_group_dn in current_dept_groups:
                                        if old_group_dn != new_dept_group_dn:
                                            groups_to_remove.append(old_group_dn)

                                    if groups_to_remove:
                                        remove_success = await ad_client.remove_user_from_groups(job_number, groups_to_remove)
                                        if remove_success:
                                            logger.info(f"已将用户 {job_number} ({hrm_user.UserName}) 从旧部门安全组移除: {groups_to_remove}")
                                            # 可以添加一个新的统计项
                                            stats["removed_from_groups"] = stats.get("removed_from_groups", 0) + len(groups_to_remove)
                                            # 记录从安全组移除的详细信息
                                            for group_dn in groups_to_remove:
                                                group_info = await ad_client.get_group_by_dn(group_dn)
                                                group_name = group_info.get('name', '') if group_info else '未知安全组'
                                                stats["removed_from_groups_details"].append({
                                                    "username": job_number,
                                                    "name": hrm_user.UserName,
                                                    "group_name": group_name,
                                                    "group_dn": group_dn
                                                })
                                        else:
                                            logger.warning(f"将用户 {job_number} 从旧部门安全组移除失败: {groups_to_remove}")
                            except Exception as e:
                                error = f"调整用户 {job_number} ({hrm_user.UserName}) 安全组时出错: {str(e)}"
                                logger.error(error)
                                stats["errors"].append(error)

                    else:
                        # 用户不存在，需要创建
                        # 生成随机密码，长度为10，包含大小写字母、数字和特殊字符
                        password = await generate_random_password()

                        # 构建用户数据
                        username = job_number
                        user_data = {
                            "username": username,
                            "name": hrm_user.UserName,
                            "email": hrm_user.Email if hrm_user.Email else "",
                            "password": password,
                            "ou_dn": target_ou_dn,
                            "department": hrm_user.DeptName if hrm_user.DeptName else "",
                            "title": hrm_user.JobTitleName if hrm_user.JobTitleName else "",
                            "change_password_next_logon": sync_data.change_password_next_logon
                        }

                        # 创建AD用户
                        user_create = schemas.ADUserCreate(**user_data)
                        await create_user(user_create)
                        stats["created_users"] += 1
                        create_count += 1  # 更新本地统计计数

                        # 记录新创建的用户和密码
                        stats["new_users"].append({
                            "username": username,
                            "name": hrm_user.UserName,
                            "password": password,
                            "department": hrm_user.DeptName if hrm_user.DeptName else "",
                        })

                        logger.info(f"已创建新用户: {username} ({hrm_user.UserName})")

                        # 如果开启了安全组创建，将新创建的用户添加到对应部门的安全组
                        if sync_data.create_security_groups and sync_data.add_users_to_dept_groups and hrm_user.DeptID:
                            if hrm_user.DeptID in dept_to_group_dn:
                                group_dn = dept_to_group_dn[hrm_user.DeptID]
                                try:
                                    # 将用户添加到部门安全组，并获取是否有实际添加操作
                                    group_success, actually_added = await ad_client.add_user_to_groups(username, [group_dn])
                                    if group_success and actually_added:
                                        stats["added_to_groups"] += 1
                                        logger.info(f"已将新用户 {username} ({hrm_user.UserName}) 添加到部门安全组")
                                        # 记录添加到安全组的详细信息
                                        group_info = await ad_client.get_group_by_dn(group_dn)
                                        group_name = group_info.get('name', '') if group_info else '未知安全组'
                                        stats["added_to_groups_details"].append({
                                            "username": username,
                                            "name": hrm_user.UserName,
                                            "group_name": group_name,
                                            "group_dn": group_dn
                                        })
                                    elif not group_success:
                                        logger.warning(f"将新用户 {username} 添加到部门安全组 {group_dn} 失败")
                                except Exception as e:
                                    error = f"将新用户 {username} 添加到安全组时出错: {str(e)}"
                                    logger.error(error)
                                    stats["errors"].append(error)

                except Exception as e:
                    error = f"处理用户 {hrm_user.UserName} 时出错: {str(e)}"
                    logger.error(error)
                    stats["errors"].append(error)

            logger.info(f"在职人员处理完成: 创建 {create_count} 个新用户, 更新 {update_count} 个用户, 移动 {move_count} 个用户到新部门, 启用 {len(to_enable)} 个用户")

        # 批量启用被禁用的在职用户
        if to_enable:
            logger.info(f"开始批量启用 {len(to_enable)} 个被禁用的在职用户账号")
            # 每批处理50个用户
            batch_size = 50
            enabled_count = 0

            for i in range(0, len(to_enable), batch_size):
                batch_users = to_enable[i:i+batch_size]
                current_batch = len(batch_users)

                logger.info(f"批量启用第 {i//batch_size + 1} 批，共 {current_batch} 个用户 ({i+current_batch}/{len(to_enable)})")
                enable_results = await ad_client.toggle_users_status_batch(batch_users, disable=False)

                # 统计成功启用的用户数
                batch_enabled = sum(1 for success in enable_results.values() if success)
                enabled_count += batch_enabled

                logger.info(f"本批次成功启用 {batch_enabled}/{current_batch} 个用户")

            logger.info(f"批量启用完成，共启用 {enabled_count}/{len(to_enable)} 个用户")

        logger.info(f"在职人员处理完成: 创建 {create_count} 个新用户, 更新 {update_count} 个用户, 移动 {move_count} 个用户到新部门, 启用 {len(to_enable)} 个用户")

        # 处理非在职人员 - 禁用账号
        disabled_count = 0
        whitelist_skipped_count = 0
        if sync_data.disable_inactive_users and non_active_users:
            logger.info(f"开始处理 {len(non_active_users)} 个非在职人员账号...")
            
            # 解析白名单
            whitelist = []
            if sync_data.disable_whitelist:
                try:
                    import json
                    if isinstance(sync_data.disable_whitelist, str):
                        whitelist = json.loads(sync_data.disable_whitelist)
                    else:
                        whitelist = sync_data.disable_whitelist
                    
                    if whitelist:
                        logger.info(f"禁用白名单已配置，包含 {len(whitelist)} 个工号: {', '.join(whitelist)}")
                    else:
                        logger.info("禁用白名单为空列表")
                except Exception as e:
                    logger.error(f"解析禁用白名单时出错: {str(e)}")
                    whitelist = []
            else:
                logger.info("未配置禁用白名单")

            # 根据工号查询对应的AD账号是否存在，存在则禁用
            for user in non_active_users:
                if not user.JobNumber:
                    continue

                logger.debug(f"检查用户: {user.JobNumber} ({user.UserName})，白名单: {whitelist}")
                
                # 检查是否在白名单中
                if whitelist and user.JobNumber in whitelist:
                    logger.info(f"跳过禁用白名单中的用户: {user.JobNumber} ({user.UserName})")
                    whitelist_skipped_count += 1
                    stats["whitelist_skipped_users"] += 1
                    stats["whitelist_skipped_users_details"].append({
                        "username": user.JobNumber,
                        "name": user.UserName,
                        "status": user.Status,
                        "department": getattr(user, 'DeptName', '') or ''
                    })
                    continue

                try:
                    # 查询AD账号状态
                    ad_user = await ad_client.get_user(user.JobNumber, no_cache=True)
                    if ad_user:
                        # 账号存在且启用状态，则禁用
                        if ad_user.get('enabled', True):
                            await ad_client.disable_user(user.JobNumber)
                            logger.info(f"已禁用非在职人员账号: {user.JobNumber} ({user.UserName})")
                            disabled_count += 1
                            stats["disabled_users"] += 1
                            stats["disabled_users_details"].append({
                                "username": user.JobNumber,
                                "name": user.UserName,
                                "status": user.Status,
                                "department": getattr(user, 'DeptName', '') or ''
                            })
                        else:
                            logger.info(f"非在职人员账号已是禁用状态: {user.JobNumber} ({user.UserName})")
                except Exception as e:
                    logger.error(f"处理非在职人员账号时出错 {user.JobNumber}: {str(e)}")

            logger.info(f"非在职人员账号处理完成，共禁用 {disabled_count} 个账号，白名单跳过 {whitelist_skipped_count} 个账号")

        # 统计总时间
        end_time = time.time()
        time_taken = round(end_time - start_time, 2)
        
        # 构建返回结果
        result = {
            "success": True,
            "message": f"从基础信息-人员信息同步完成，耗时 {time_taken} 秒",
            "stats": stats
        }
        
        # 记录详细日志
        logger.info(f"同步完成：创建用户 {stats['created_users']} 个，更新用户 {stats['updated_users']} 个，移动用户 {stats['moved_users']} 个，禁用用户 {stats['disabled_users']} 个")
        
        return result
    except Exception as e:
        logger.error(f"同步过程中出现异常: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"同步过程中出现异常: {str(e)}",
            "stats": {
                "errors": [str(e)]
            }
        }

async def find_ou_by_dept_id(dept_id: int, dept_name: str, ou_tree: List, created_ous: Dict = None) -> Optional[str]:
    """通过部门ID查找OU的DN，避免同名OU冲突

    Args:
        dept_id: 部门ID
        dept_name: 部门名称
        ou_tree: OU树
        created_ous: 已创建的OU映射

    Returns:
        找到的OU DN或None
    """
    # 首先从created_ous中查找精确匹配
    if created_ous:
        dept_key = f"dept_{dept_id}"
        if dept_key in created_ous:
            logger.info(f"从created_ous中找到部门 {dept_name} (ID: {dept_id}) 的OU: {created_ous[dept_key]}")
            return created_ous[dept_key]

    # 然后尝试通过名称查找
    matching_ous = []

    def search_ou_tree(nodes, path=""):
        for node in nodes:
            current_path = f"{path}/{node.name}" if path else node.name
            # 检查是否匹配
            if node.name == dept_name:
                matching_ous.append((current_path, node.dn))
            # 递归搜索子节点
            if hasattr(node, 'children') and node.children:
                search_ou_tree(node.children, current_path)

    # 搜索OU树
    search_ou_tree(ou_tree)

    # 如果只找到一个匹配项，直接返回
    if len(matching_ous) == 1:
        logger.info(f"通过名称唯一匹配找到部门 {dept_name} 的OU: {matching_ous[0][1]}")
        return matching_ous[0][1]
    # 如果找到多个匹配项，记录错误
    elif len(matching_ous) > 1:
        paths = "\n - ".join([path for path, _ in matching_ous])
        error_msg = f"找到多个匹配的OU '{dept_name}'，请使用完整路径指定： - {paths}"
        logger.warning(error_msg)
        # 使用第一个匹配项，但记录警告
        logger.info(f"使用第一个匹配项: {matching_ous[0][1]}")
        return matching_ous[0][1]

    # 没有找到匹配项
    logger.warning(f"未在OU树中找到部门 {dept_name} (ID: {dept_id}) 的OU")
    return None

def build_group_name_for_dept(dept: dict, all_departments: List[dict], duplicate_dept_names: set = None) -> str:
    """为部门构建友好的安全组名称

    Args:
        dept: 部门信息
        all_departments: 所有部门列表
        duplicate_dept_names: 重名部门集合，如果为None则自动计算

    Returns:
        安全组名称
    """
    # 如果未提供重名部门集合，则计算
    if duplicate_dept_names is None:
        dept_names_count = {}
        for d in all_departments:
            if d.get('DeptName'):
                dept_names_count[d['DeptName']] = dept_names_count.get(d['DeptName'], 0) + 1
        duplicate_dept_names = {name for name, count in dept_names_count.items() if count > 1}

    # 基本安全组名称 - 去掉DEPT_前缀
    base_name = dept['DeptName']

    # 如果是重名部门，添加区分信息
    if dept['DeptName'] in duplicate_dept_names:
        # 尝试获取父部门名称作为区分
        parent_dept = None
        if dept.get('SuperiorID'):
            parent_dept = next((d for d in all_departments if d.get('ID') == dept['SuperiorID']), None)

        if parent_dept and parent_dept.get('DeptName'):
            # 使用父部门名称区分，父部门名称在前
            group_name = f"{parent_dept['DeptName']}_{base_name}"
        elif dept.get('CompanyName'):
            # 使用公司名称区分，公司名称在前
            group_name = f"{dept['CompanyName']}_{base_name}"
        else:
            # 最后才使用ID作为区分 - 对人类更友好的格式
            group_name = f"{base_name}_{dept['ID']}"
    else:
        # 非重名部门使用简单名称
        group_name = base_name

    # 限制名称长度，避免过长（AD限制）
    if len(group_name) > 64:
        # 截断名称，保留基本信息
        max_len = 60  # 留4个字符给"..."
        group_name = f"{group_name[:max_len]}..."

    # 替换特殊字符，以确保安全组名称有效
    group_name = group_name.replace('/', '_').replace('\\', '_').replace(',', '_')

    return group_name

def extract_dept_id_from_group(group_desc: str) -> Optional[int]:
    """从安全组描述中提取部门ID

    Args:
        group_desc: 安全组描述

    Returns:
        部门ID或None（如果未找到）
    """
    # 确保输入是字符串类型
    if group_desc is None:
        return None

    if not isinstance(group_desc, str):
        try:
            group_desc = str(group_desc)
        except:
            return None

    if not group_desc:
        return None

    # 使用更健壮的正则表达式，支持中英文冒号和可选空格
    # 匹配格式：部门ID: 123、部门ID:123、部门ID： 123、部门ID：123
    match = re.search(r'部门ID[：:]\s*(\d+)', group_desc)
    if match:
        try:
            return int(match.group(1))
        except ValueError:
            return None
    return None

async def handle_department_changes(updated_departments: List[dict]) -> Dict:
    """处理部门变更，更新对应的安全组

    Args:
        updated_departments: 更新的部门列表，每个部门包含ID和新的信息

    Returns:
        处理结果
    """
    try:
        logger.info(f"开始处理 {len(updated_departments)} 个部门的变更")

        # 统计
        stats = {
            "total": len(updated_departments),
            "updated_groups": 0,
            "renamed_groups": 0,
            "errors": []
        }

        # 获取所有部门信息，用于构建新的安全组名称
        try:
            from app.api.v1.ecology import get_ecology_departments
            all_departments = get_ecology_departments()
            logger.info(f"获取到 {len(all_departments)} 个部门信息")
        except Exception as e:
            logger.error(f"获取部门信息时出错: {str(e)}")
            stats["errors"].append(f"获取部门信息失败: {str(e)}")
            all_departments = []

        # 收集所有部门名称，检查是否有重名
        dept_names_count = {}
        for dept in all_departments:
            if dept.get('DeptName'):
                dept_names_count[dept['DeptName']] = dept_names_count.get(dept['DeptName'], 0) + 1

        # 记录重名的部门
        duplicate_dept_names = {name for name, count in dept_names_count.items() if count > 1}

        # 处理每个更新的部门
        for dept in updated_departments:
            dept_id = dept.get('ID')
            if not dept_id:
                continue

            try:
                # 查找部门对应的安全组
                group = await ad_client.find_group_by_dept_id(dept_id)
                if group:
                    # 获取完整的部门信息
                    full_dept = next((d for d in all_departments if d.get('ID') == dept_id), dept)

                    # 构建新的安全组名称
                    new_group_name = build_group_name_for_dept(full_dept, all_departments, duplicate_dept_names)

                    # 构建新的描述
                    new_desc = f"部门 {full_dept.get('DeptName', '')} 的安全组 (部门ID: {dept_id})"

                    # 检查是否需要更新
                    needs_rename = group['name'] != new_group_name

                    # 安全处理描述为None的情况
                    description = group.get('description')
                    if description is None:
                        description = ''

                    needs_desc_update = not description or (extract_dept_id_from_group(description) is None)

                    if needs_rename or needs_desc_update:
                        updates = {}

                        if needs_rename:
                            updates['name'] = new_group_name
                            logger.info(f"部门 {dept_id} 的安全组需要重命名: {group['name']} -> {new_group_name}")

                        if needs_desc_update:
                            updates['description'] = new_desc
                            logger.info(f"部门 {dept_id} 的安全组需要更新描述")

                        # 更新安全组
                        success = await ad_client.update_group(group['dn'], updates)

                        if success:
                            if needs_rename:
                                stats["renamed_groups"] += 1
                            stats["updated_groups"] += 1
                            logger.info(f"成功更新部门 {dept_id} 的安全组")
                        else:
                            error_msg = f"更新部门 {dept_id} 的安全组失败"
                            logger.error(error_msg)
                            stats["errors"].append(error_msg)
                else:
                    logger.info(f"未找到部门 {dept_id} 对应的安全组，可能需要创建")

            except Exception as e:
                error_msg = f"处理部门 {dept_id} 变更时出错: {str(e)}"
                logger.error(error_msg)
                stats["errors"].append(error_msg)

        logger.info(f"部门变更处理完成: 共处理 {stats['total']} 个部门，重命名了 {stats['renamed_groups']} 个安全组，更新了 {stats['updated_groups']} 个安全组")
        return {
            "message": f"部门变更处理完成: 共处理 {stats['total']} 个部门，重命名了 {stats['renamed_groups']} 个安全组，更新了 {stats['updated_groups']} 个安全组",
            "stats": stats
        }

    except Exception as e:
        logger.error(f"处理部门变更时出错: {str(e)}")
        return {
            "message": f"处理部门变更失败: {str(e)}",
            "stats": {
                "total": len(updated_departments),
                "updated_groups": 0,
                "renamed_groups": 0,
                "errors": [str(e)]
            }
        }

async def validate_ou_dn(ou_dn: str) -> bool:
    """
    验证OU的可用性和有效性，检查OU是否存在

    Args:
        ou_dn: 组织单位的DN

    Returns:
        OU是否有效且存在
    """
    try:
        # 基本格式验证
        if not ou_dn or not isinstance(ou_dn, str):
            logger.error(f"Invalid OU DN format: {ou_dn}")
            return False

        # 检查必须包含OU=或DC=部分
        if "OU=" not in ou_dn and "DC=" not in ou_dn:
            logger.error(f"OU DN must contain OU= or DC= component: {ou_dn}")
            return False

        # 使用AD客户端验证OU是否存在
        exists = await ad_client.check_ou_exists(ou_dn)
        if not exists:
            logger.error(f"OU does not exist: {ou_dn}")
            return False

        return True
    except Exception as e:
        logger.error(f"Error validating OU DN {ou_dn}: {str(e)}")
        return False

async def move_security_group(group_dn: str, target_ou_dn: str, group_name: str = None) -> Dict[str, Any]:
    """移动安全组到指定的OU

    Args:
        group_dn: 安全组的DN
        target_ou_dn: 目标OU的DN
        group_name: 安全组名称(可选，用于日志)

    Returns:
        Dict: 包含操作结果的字典
            - success: 是否成功
            - message: 成功或失败的消息
            - error: 如果有错误，详细错误信息
    """
    try:
        # 如果没有提供名称，从DN中提取
        if not group_name:
            # 尝试从DN提取组名
            dn_parts = group_dn.split(',')
            if dn_parts and dn_parts[0].startswith('CN='):
                group_name = dn_parts[0][3:]  # 去掉'CN='前缀
            else:
                group_name = "未知安全组"

        # 验证目标OU是否存在
        if not await ad_client.check_ou_exists(target_ou_dn):
            error_msg = f"目标OU '{target_ou_dn}' 不存在"
            logger.error(error_msg)
            return {
                "success": False,
                "message": f"移动安全组 '{group_name}' 失败",
                "error": error_msg
            }

        # 执行移动操作
        result = await ad_client.move_object(group_dn, target_ou_dn)

        if result:
            logger.info(f"已成功移动安全组 '{group_name}' 到 {target_ou_dn}")
            return {
                "success": True,
                "message": f"已成功移动安全组 '{group_name}' 到目标OU"
            }
        else:
            error_msg = f"移动安全组到目标OU失败"
            logger.error(f"{error_msg}: {group_name} -> {target_ou_dn}")
            return {
                "success": False,
                "message": f"移动安全组 '{group_name}' 失败",
                "error": error_msg
            }

    except Exception as e:
        error_msg = f"移动安全组 '{group_name}' 时出错: {str(e)}"
        tb = traceback.format_exc()
        logger.error(f"{error_msg}\n{tb}")
        return {
            "success": False,
            "message": f"移动安全组 '{group_name}' 失败",
            "error": error_msg
        }

async def validate_group_name(name: str) -> bool:
    """
    验证安全组名称的有效性
    
    Args:
        name: 安全组名称
        
    Returns:
        bool: 名称是否有效
    """
    try:
        # 基本格式验证
        if not name or not isinstance(name, str):
            logger.error(f"Invalid group name format: {name}")
            return False
            
        # 安全组名称长度限制 (AD限制组名最长64个字符)
        if len(name) > 64:
            logger.error(f"Group name too long (max 64 chars): {name}")
            return False
            
        # 检查非法字符
        invalid_chars = ['/', '\\', '[', ']', ':', ';', '|', '=', '+', '*', '?', '<', '>', '"', ',']
        for char in invalid_chars:
            if char in name:
                logger.error(f"Group name contains invalid character '{char}': {name}")
                return False
                
        return True
    except Exception as e:
        logger.error(f"Error validating group name {name}: {str(e)}")
        return False