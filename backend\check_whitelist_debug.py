#!/usr/bin/env python3
"""
检查AD同步白名单功能的调试脚本
"""
import sys
import os
import json
import asyncio
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_database_connection():
    """检查数据库连接"""
    try:
        from app.database import SessionLocal
        db = SessionLocal()
        print("✅ 数据库连接成功")
        db.close()
        return True
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def check_ad_sync_config():
    """检查AD同步配置"""
    try:
        from app.database import SessionLocal
        from app.models.ad_config import ADSyncConfig
        
        db = SessionLocal()
        config = db.query(ADSyncConfig).first()
        
        if not config:
            print("⚠️ 未找到AD同步配置")
            return None
            
        print(f"✅ 找到AD同步配置 (ID: {config.id})")
        print(f"  - 启用状态: {config.enabled}")
        print(f"  - 禁用离职人员: {config.disable_inactive_users}")
        print(f"  - 白名单原始数据: {repr(config.disable_whitelist)}")
        print(f"  - 白名单数据类型: {type(config.disable_whitelist)}")
        
        # 尝试解析白名单
        if config.disable_whitelist:
            try:
                if isinstance(config.disable_whitelist, str):
                    whitelist = json.loads(config.disable_whitelist)
                    print(f"  - 解析后白名单: {whitelist}")
                    print(f"  - 解析后类型: {type(whitelist)}")
                else:
                    print(f"  - 白名单不是字符串类型: {config.disable_whitelist}")
            except Exception as e:
                print(f"  - 白名单解析失败: {e}")
        else:
            print("  - 白名单为空")
            
        db.close()
        return config
        
    except Exception as e:
        print(f"❌ 检查AD同步配置失败: {e}")
        return None

def check_recent_sync_logs():
    """检查最近的同步日志"""
    try:
        from app.database import SessionLocal
        from app.models.ad_config import ADSyncLog
        from sqlalchemy import desc
        
        db = SessionLocal()
        recent_logs = db.query(ADSyncLog).order_by(desc(ADSyncLog.sync_time)).limit(5).all()
        
        if not recent_logs:
            print("⚠️ 未找到同步日志")
            return
            
        print(f"✅ 找到 {len(recent_logs)} 条最近的同步日志:")
        for i, log in enumerate(recent_logs, 1):
            print(f"  {i}. 时间: {log.sync_time}")
            print(f"     操作员: {log.operator}")
            print(f"     禁用用户数: {log.disabled_users}")
            print(f"     跳过用户数: {getattr(log, 'whitelist_skipped_users', 'N/A')}")
            
            # 检查详细信息中的白名单相关数据
            if log.details:
                try:
                    details = json.loads(log.details)
                    stats = details.get('stats', {})
                    if 'whitelist_skipped_users_details' in stats:
                        skipped_details = stats['whitelist_skipped_users_details']
                        print(f"     白名单跳过详情: {len(skipped_details)} 个用户")
                        for user in skipped_details[:3]:  # 只显示前3个
                            print(f"       - {user.get('username', 'N/A')} ({user.get('name', 'N/A')})")
                except Exception as e:
                    print(f"     详情解析失败: {e}")
            print()
            
        db.close()
        
    except Exception as e:
        print(f"❌ 检查同步日志失败: {e}")

def check_personnel_data():
    """检查人员数据中的离职人员"""
    try:
        from app.database import SessionLocal
        from app.models.ecology import EcologyUser
        
        db = SessionLocal()
        
        # 查询离职人员
        inactive_users = db.query(EcologyUser).filter(
            EcologyUser.Status.in_(['离职', '停用', '禁用'])
        ).limit(10).all()
        
        if not inactive_users:
            print("⚠️ 未找到离职人员数据")
            return
            
        print(f"✅ 找到 {len(inactive_users)} 个离职人员 (显示前10个):")
        for user in inactive_users:
            print(f"  - {user.JobNumber} ({user.UserName}) - 状态: {user.Status}")
            
        db.close()
        
    except Exception as e:
        print(f"❌ 检查人员数据失败: {e}")

def simulate_whitelist_logic():
    """模拟白名单逻辑"""
    print("\n=== 模拟白名单逻辑测试 ===")
    
    # 模拟配置
    sync_data_whitelist = ["EMP001", "EMP003"]
    
    # 模拟离职用户
    mock_users = [
        {"JobNumber": "EMP001", "UserName": "张三", "Status": "离职"},
        {"JobNumber": "EMP002", "UserName": "李四", "Status": "离职"},
        {"JobNumber": "EMP003", "UserName": "王五", "Status": "离职"},
        {"JobNumber": "EMP004", "UserName": "赵六", "Status": "离职"}
    ]
    
    print(f"白名单配置: {sync_data_whitelist}")
    print(f"离职用户: {[f'{u[\"JobNumber\"]}({u[\"UserName\"]})' for u in mock_users]}")
    
    # 模拟白名单逻辑
    whitelist = []
    if sync_data_whitelist:
        try:
            if isinstance(sync_data_whitelist, str):
                whitelist = json.loads(sync_data_whitelist)
            else:
                whitelist = sync_data_whitelist
                
            if whitelist:
                print(f"解析后白名单: {whitelist} (包含 {len(whitelist)} 个工号)")
            else:
                print("白名单为空列表")
        except Exception as e:
            print(f"解析白名单时出错: {e}")
            whitelist = []
    else:
        print("未配置白名单")
    
    # 处理每个用户
    disabled_count = 0
    whitelist_skipped_count = 0
    
    for user in mock_users:
        if not user["JobNumber"]:
            continue
            
        print(f"\n检查用户: {user['JobNumber']} ({user['UserName']})")
        print(f"  白名单: {whitelist}")
        
        # 检查是否在白名单中
        if whitelist and user["JobNumber"] in whitelist:
            print(f"  ✅ 跳过禁用白名单中的用户: {user['JobNumber']} ({user['UserName']})")
            whitelist_skipped_count += 1
        else:
            print(f"  ❌ 将要禁用用户: {user['JobNumber']} ({user['UserName']})")
            disabled_count += 1
    
    print(f"\n模拟结果:")
    print(f"  - 将禁用: {disabled_count} 个用户")
    print(f"  - 白名单跳过: {whitelist_skipped_count} 个用户")

def main():
    """主函数"""
    print("=== AD同步白名单功能检查 ===")
    print(f"检查时间: {datetime.now()}")
    print()
    
    # 1. 检查数据库连接
    print("1. 检查数据库连接...")
    if not check_database_connection():
        return
    print()
    
    # 2. 检查AD同步配置
    print("2. 检查AD同步配置...")
    config = check_ad_sync_config()
    print()
    
    # 3. 检查最近的同步日志
    print("3. 检查最近的同步日志...")
    check_recent_sync_logs()
    print()
    
    # 4. 检查人员数据
    print("4. 检查人员数据...")
    check_personnel_data()
    print()
    
    # 5. 模拟白名单逻辑
    simulate_whitelist_logic()
    print()
    
    print("=== 检查完成 ===")

if __name__ == "__main__":
    main()
