"""add_disable_whitelist_to_ad_sync_config

Revision ID: a8c70c74655e
Revises: modify_ad_sync_logs_columns
Create Date: 2025-08-07 10:35:29.149518

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'a8c70c74655e'
down_revision: Union[str, None] = 'modify_ad_sync_logs_columns'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('ad_sync_config', sa.Column('disable_whitelist', sa.String(), nullable=True))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('ad_sync_config', 'disable_whitelist')
    # ### end Alembic commands ###
