#!/usr/bin/env python3
"""
直接测试白名单功能
"""
import sys
import os
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    print("开始测试白名单功能...")
    
    # 测试数据库连接
    print("1. 测试数据库连接...")
    from app.database import SessionLocal
    db = SessionLocal()
    print("✅ 数据库连接成功")
    
    # 测试AD同步配置
    print("2. 测试AD同步配置...")
    from app.models.ad_config import ADSyncConfig
    configs = db.query(ADSyncConfig).all()
    print(f"找到 {len(configs)} 个AD同步配置")
    
    for config in configs:
        print(f"  配置ID: {config.id}")
        print(f"  启用状态: {config.enabled}")
        print(f"  禁用离职人员: {config.disable_inactive_users}")
        print(f"  白名单原始数据: {repr(config.disable_whitelist)}")
        
        if config.disable_whitelist:
            try:
                if isinstance(config.disable_whitelist, str):
                    parsed = json.loads(config.disable_whitelist)
                    print(f"  解析后白名单: {parsed}")
                    print(f"  白名单包含 {len(parsed)} 个工号")
                else:
                    print(f"  白名单不是字符串: {type(config.disable_whitelist)}")
            except Exception as e:
                print(f"  白名单解析失败: {e}")
        else:
            print("  白名单为空")
    
    # 测试同步日志
    print("3. 测试同步日志...")
    from app.models.ad_config import ADSyncLog
    from sqlalchemy import desc
    logs = db.query(ADSyncLog).order_by(desc(ADSyncLog.sync_time)).limit(3).all()
    print(f"找到 {len(logs)} 条最近的同步日志")
    
    for log in logs:
        print(f"  日志ID: {log.id}")
        print(f"  操作员: {log.operator}")
        print(f"  同步时间: {log.sync_time}")
        print(f"  禁用用户数: {log.disabled_users}")
        
        if log.details:
            try:
                details_dict = json.loads(log.details)
                stats = details_dict.get('stats', {})
                whitelist_skipped = stats.get('whitelist_skipped_users', 0)
                print(f"  白名单跳过用户数: {whitelist_skipped}")
            except Exception as e:
                print(f"  详情解析失败: {e}")
    
    # 测试离职人员数据
    print("4. 测试离职人员数据...")
    from app.models.ecology import EcologyUser
    inactive_users = db.query(EcologyUser).filter(
        EcologyUser.Status.in_(['离职', '停用', '禁用'])
    ).limit(5).all()
    print(f"找到 {len(inactive_users)} 个离职人员")
    
    for user in inactive_users:
        print(f"  - {user.JobNumber} ({user.UserName}) - {user.Status}")
    
    # 模拟白名单逻辑
    print("5. 模拟白名单逻辑...")
    test_whitelist = ["EMP001", "EMP003"]
    mock_users = [
        {"JobNumber": "EMP001", "UserName": "张三", "Status": "离职"},
        {"JobNumber": "EMP002", "UserName": "李四", "Status": "离职"},
        {"JobNumber": "EMP003", "UserName": "王五", "Status": "离职"},
        {"JobNumber": "EMP004", "UserName": "赵六", "Status": "离职"}
    ]
    
    print(f"测试白名单: {test_whitelist}")
    disabled_count = 0
    whitelist_skipped_count = 0
    
    for user in mock_users:
        if test_whitelist and user["JobNumber"] in test_whitelist:
            print(f"  ✅ 跳过白名单用户: {user['JobNumber']} ({user['UserName']})")
            whitelist_skipped_count += 1
        else:
            print(f"  ❌ 将禁用用户: {user['JobNumber']} ({user['UserName']})")
            disabled_count += 1
    
    print(f"模拟结果: 将禁用 {disabled_count} 个用户, 白名单跳过 {whitelist_skipped_count} 个用户")
    
    db.close()
    print("✅ 测试完成")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
