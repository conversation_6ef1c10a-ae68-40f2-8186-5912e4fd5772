from sqlalchemy import Column, Integer, String, <PERSON><PERSON><PERSON>, ForeignKey
from ..database import Base

class ADConfig(Base):
    __tablename__ = "ad_config"

    id = Column(Integer, primary_key=True, index=True)
    server = Column(String, nullable=False)
    domain = Column(String, nullable=False)
    username = Column(String, nullable=False)
    password = Column(String, nullable=False)
    search_base = Column(String, nullable=False)
    use_ssl = Column(Boolean, default=False)
    port = Column(Integer, default=389)

class ADSyncConfig(Base):
    __tablename__ = "ad_sync_config"

    id = Column(Integer, primary_key=True, index=True)
    enabled = Column(Boolean, default=False)
    source_company_id = Column(Integer, nullable=True)
    source_dept_id = Column(Integer, nullable=True)
    target_ou_dn = Column(String, nullable=False)
    create_ou = Column(Boolean, default=True)
    create_security_groups = Column(<PERSON><PERSON><PERSON>, default=False)  # 是否自动创建部门安全组
    add_users_to_dept_groups = Column(Boolean, default=False)  # 是否自动将用户添加到对应部门安全组
    change_password_next_logon = Column(Boolean, default=True)
    disable_inactive_users = Column(Boolean, default=True)  # 是否禁用非在职人员的AD账号
    disable_whitelist = Column(String, nullable=True)  # 禁用白名单，JSON格式存储工号列表
    move_users_with_dept = Column(Boolean, default=True)  # 是否在人员部门变更时自动移动用户到对应的OU
    update_user_groups_with_dept = Column(Boolean, default=False)  # 是否在人员部门变更时自动调整安全组
    auto_rename_security_groups = Column(Boolean, default=False)  # 是否自动修正被人为修改的安全组名称
    sync_interval = Column(Integer, default=24)  # 同步间隔，单位小时
    sync_time = Column(String, nullable=True)  # 指定的同步时间，格式HH:MM
    last_sync_time = Column(String, nullable=True)  # 上次同步时间 