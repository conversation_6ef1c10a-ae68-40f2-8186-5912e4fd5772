from pydantic import BaseModel
from typing import Optional, List

class ADConfigBase(BaseModel):
    server: str
    domain: str
    username: str
    search_base: str

class ADConfigCreate(ADConfigBase):
    password: str

class ADConfigUpdate(ADConfigCreate):
    pass

class ADConfigResponse(ADConfigBase):
    id: int
    password: str

    class Config:
        from_attributes = True

class ADSyncConfigBase(BaseModel):
    enabled: bool = False
    source_company_id: Optional[int] = None
    source_dept_id: Optional[int] = None
    target_ou_dn: str
    create_ou: bool = True
    create_security_groups: bool = False  # 是否自动创建部门安全组
    add_users_to_dept_groups: bool = False  # 是否自动将用户添加到对应部门安全组
    change_password_next_logon: bool = True
    disable_inactive_users: bool = True  # 是否禁用非在职人员的AD账号
    disable_whitelist: Optional[List[str]] = []  # 禁用白名单，工号列表
    move_users_with_dept: bool = True  # 是否在人员部门变更时自动移动用户到对应的OU
    update_user_groups_with_dept: bool = False  # 是否在人员部门变更时自动调整安全组
    auto_rename_security_groups: bool = False  # 是否自动修正被人为修改的安全组名称
    sync_interval: int = 24
    sync_time: Optional[str] = None  # 指定的同步时间，格式HH:MM
    
class ADSyncConfigCreate(ADSyncConfigBase):
    pass

class ADSyncConfigUpdate(ADSyncConfigBase):
    pass

class ADSyncConfigResponse(ADSyncConfigBase):
    id: int
    last_sync_time: Optional[str] = None
    
    class Config:
        from_attributes = True 