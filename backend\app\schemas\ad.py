from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List
import re

class OUBase(BaseModel):
    name: str
    description: Optional[str] = None

class OUCreate(OUBase):
    parent_dn: Optional[str] = None

class OUUpdate(OUBase):
    pass

class OUResponse(OUBase):
    dn: str
    children: Optional[List['OUResponse']] = None

    class Config:
        from_attributes = True

class ADUserBase(BaseModel):
    name: str
    username: str
    email: Optional[str] = None

class ADUserCreate(BaseModel):
    username: str
    name: str
    email: Optional[str] = None
    password: str
    department: Optional[str] = None
    title: Optional[str] = None
    ou_dn: str
    groups: Optional[List[str]] = None
    change_password_next_logon: bool = False

class ADUserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[str] = None
    department: Optional[str] = None
    title: Optional[str] = None
    password: Optional[str] = None
    groups: Optional[List[str]] = None

class ADUserResponse(BaseModel):
    username: str
    name: str
    email: Optional[str] = ''
    department: Optional[str] = ''
    title: Optional[str] = ''
    enabled: bool = True
    groups: List[str] = []
    dn: str
    password_expiry_date: Optional[str] = None
    password_never_expires: bool = False
    days_until_expiry: Optional[int] = None
    password_expired: bool = False

    class Config:
        from_attributes = True

class ADGroupBase(BaseModel):
    name: str = Field(..., max_length=256)
    description: Optional[str] = Field(None, max_length=255)

class ADGroupCreate(ADGroupBase):
    ou_dn: str

class ADGroupUpdate(BaseModel):
    description: Optional[str] = None

class ADGroupResponse(ADGroupBase):
    dn: str
    memberCount: int

    class Config:
        from_attributes = True

class ADConfigBase(BaseModel):
    server: str
    domain: str
    username: str
    search_base: str
    use_ssl: bool = False
    port: int = Field(default=389)

class ADConfigCreate(ADConfigBase):
    password: str

class ADConfigUpdate(ADConfigBase):
    password: Optional[str] = None

class ADConfigResponse(ADConfigBase):
    id: int
    is_active: bool = True

    class Config:
        from_attributes = True

class ADUserImport(BaseModel):
    username: str = Field(
        ...,
        min_length=2,
        max_length=64,
        description="用户登录名，只能包含字母、数字、下划线",
        example="zhangsan"
    )
    name: str = Field(
        ...,
        min_length=2,
        max_length=64,
        description="显示名称，建议使用中文名",
        example="张三"
    )
    email: Optional[str] = Field(
        None,
        description="邮箱地址",
        example="<EMAIL>"
    )
    password: Optional[str] = Field(
        None,
        min_length=8,
        max_length=32,
        description="密码，留空将自动生成随机密码",
    )
    department: Optional[str] = Field(
        None,
        max_length=128,
        description="部门名称",
        example="IT部"
    )
    title: Optional[str] = Field(
        None,
        max_length=128,
        description="职务名称",
        example="工程师"
    )
    ou_dn: Optional[str] = Field(
        None,
        description="组织单位路径，使用斜杠(/)分隔，留空将使用当前选中的OU",
        example="IT部/开发组"
    )
    groups: Optional[List[str]] = Field(
        None,
        description="用户组名称列表，留空则不分配组",
        example=["IT组", "管理员组"]
    )
    change_password_next_logon: Optional[bool] = Field(
        False,
        description="是否要求用户下次登录时修改密码",
        example=True
    )

    class Config:
        from_attributes = True

    @validator('username')
    def username_must_be_valid(cls, v):
        if not re.match(r'^[a-zA-Z0-9_]+$', v):
            raise ValueError('用户名只能包含字母、数字和下划线')
        return v

    @validator('password')
    def validate_password_complexity(cls, v):
        if v is None:  # 如果密码为空，跳过验证
            return v

        # 密码复杂度要求
        if len(v) < 8:
            raise ValueError('密码长度至少为8个字符')

        if not re.search(r'[A-Z]', v):
            raise ValueError('密码必须包含至少一个大写字母')

        if not re.search(r'[a-z]', v):
            raise ValueError('密码必须包含至少一个小写字母')

        if not re.search(r'\d', v):
            raise ValueError('密码必须包含至少一个数字')

        if not re.search(r'[!@#$%^&*]', v):
            raise ValueError('密码必须包含至少一个特殊字符(!@#$%^&*)')

        return v

    @validator('ou_dn')
    def validate_ou_dn(cls, v):
        if v is None or not v.strip():  # 如果为空，跳过验证
            return None

        # 验证OU路径格式
        if '//' in v:
            raise ValueError('OU路径不能包含连续的斜杠')

        # 检查每个OU名称是否合法
        for ou_name in v.split('/'):
            if not ou_name.strip():
                raise ValueError('OU名称不能为空')
            if len(ou_name) > 64:
                raise ValueError(f'OU名称长度不能超过64个字符: {ou_name}')

        return v

    @validator('email')
    def validate_email_format(cls, v):
        if v is None or v.strip() == '':  # 如果邮箱为空，跳过验证
            return None

        # 简单的邮箱格式验证
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, v):
            raise ValueError('无效的邮箱格式')

        return v

    @validator('groups')
    def validate_groups(cls, v):
        if v is None:
            return v

        if not isinstance(v, list):
            raise ValueError('用户组必须是列表格式')

        # 验证组名长度
        for group_name in v:
            if not group_name.strip():
                raise ValueError('组名不能为空')
            if len(group_name) > 64:
                raise ValueError(f'组名长度不能超过64个字符: {group_name}')

        return v

class ADSyncFromPersonnel(BaseModel):
    """从基础信息-人员信息同步到AD域的参数"""
    company_id: Optional[int] = None  # 如果按公司同步则填写
    dept_id: Optional[int] = None  # 如果按部门同步则填写
    create_ou: bool = True  # 是否创建对应的组织结构(OU)
    create_security_groups: bool = False  # 是否自动创建部门安全组
    add_users_to_dept_groups: bool = False  # 是否自动将用户添加到对应部门安全组
    parent_ou_dn: str  # 创建OU的父级OU
    change_password_next_logon: bool = True  # 是否要求用户首次登录时修改密码
    disable_inactive_users: bool = True  # 是否禁用非在职人员的AD账号
    disable_whitelist: Optional[List[str]] = []  # 禁用白名单，工号列表
    move_users_with_dept: bool = True  # 是否在人员部门变更时自动移动用户到对应的OU
    update_user_groups_with_dept: bool = False  # 是否在人员部门变更时自动调整安全组
    auto_rename_security_groups: bool = False  # 是否自动修正被人为修改的安全组名称

    def __init__(self, **data):
        super().__init__(**data)
        # 处理依赖选项的逻辑关系
        # 如果未启用创建安全组，则相关选项强制设为False
        if not self.create_security_groups:
            self.add_users_to_dept_groups = False
            self.update_user_groups_with_dept = False
            self.auto_rename_security_groups = False

class ADSyncOrganizationStructure(BaseModel):
    """从泛微系统同步组织结构到AD域的参数"""
    parent_ou_dn: str  # 创建OU的父级OU
    company_id: Optional[int] = None  # 如果按公司同步则填写
    dept_id: Optional[int] = None  # 如果按部门同步则填写

__all__ = [
    'OUBase', 'OUCreate', 'OUUpdate', 'OUResponse',
    'ADUserBase', 'ADUserCreate', 'ADUserUpdate', 'ADUserResponse',
    'ADGroupBase', 'ADGroupCreate', 'ADGroupUpdate', 'ADGroupResponse',
    'ADConfigBase', 'ADConfigCreate', 'ADConfigUpdate', 'ADConfigResponse',
    'ADUserImport', 'ADSyncFromPersonnel', 'ADSyncOrganizationStructure'
]